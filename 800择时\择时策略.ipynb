from technologyindicators import *
import pymysql

config = {
    'host': '*************',
    'port': 3306,
    'user': 'ct_wind_user',
    'password': 'Ctjg2025',
    'database': 'windsh'
}
db = pymysql.connect(**config)

def sql_to_df(db, sql_query):
    cursor=db.cursor()
    cursor.execute(sql_query)
    info = cursor.fetchall()
    cols = cursor.description
    col = []
    for i in cols:
        col.append(i[0])
    data = pd.DataFrame(info,columns=col)
    return data

# 获取指数信息
index = sql_to_df(db,'select * from aindexdescription')

index[index['S_INFO_NAME'] == '中证800']

# 读取中证800量价数据
sql = '''
select * from aindexeodprices 
WHERE `S_INFO_WINDCODE` = '000906.SH' and `TRADE_DT` = '20250702'
'''
sql_to_df(db,sql)

# 读取中证800量价数据
sql = '''
select * from aindexeodprices
'''
indexprice = sql_to_df(db, sql)
# indexprice.to_csv('indexprice.csv')

indexprice.groupby('S_INFO_WINDCODE').apply(lambda x: x['TRADE_DT'].min()).rename('start_date').sort_values()

zz800_price = indexprice[indexprice['S_INFO_WINDCODE'] == '000906.SH']
zz800_price

sql = '''
select * from ashareeodprices
'''
ashareprice = sql_to_df(db, sql)

def calculate_annualized_return_from_series(price_series, periods_per_year=252):
    """
    基于价格序列计算年化收益率
    
    参数:
    price_series: 价格序列 (pandas Series 或 list)
    periods_per_year: 每年的交易周期数 (股票通常是252个交易日，月度数据是12)
    
    返回:
    年化收益率 (小数形式)
    """
    
    if isinstance(price_series, list):
        price_series = pd.Series(price_series)
    
    # 计算总收益率
    total_return = (price_series.iloc[-1] / price_series.iloc[0]) - 1
    
    # 计算持有周期数
    periods = len(price_series) - 1
    
    # 计算年化收益率
    if periods <= 0:
        raise ValueError("价格序列至少需要2个数据点")
    
    annualized_return = (1 + total_return) ** (periods_per_year / periods) - 1
    
    return annualized_return

annual_return = calculate_annualized_return_from_series(zz800['close'], periods_per_year=252)
print(f"年化收益率: {annual_return:.2%}")


tech_800 = TechnologyIndicators(zz800)
signal_800 = tech_800.calculate_factor('input_8.xlsx')
backtest_output_800 = tech_800.backtest(signal_800, mode=1, commission=0.0003)
metrics_800 = tech_800.calculate_metrics(backtest_output_800)
# metrics_800.to_csv('metrics_800.csv', index=False)
# 信号有效性检验
signal_returns_800 = tech_800.calculate_signal_returns(signal_800, [1, 3, 5, 10, 20, 30, 60, 120])
signal_returns_800.sort_values(by=['factor','direction','n_days']).to_csv('signal_returns_800.csv', index=False)

metrics_800

from main import *

strategy = CSI800TimingStrategy()

# 运行策略
# results = strategy.run_strategy()

# 运行单因子策略
results = strategy.run_single_factor_backtest('production_score')

config = StrategyConfig()
# 初始化各个模块
db = DatabaseManager()
factor_scoring = FactorScoring(db)
backtest_engine = BacktestEngine(db)
visualization = Visualization()

factor_data = factor_scoring.macro_factors.calculate_production_factor().dropna()
factor_data['code'] = '000906.SH'
factor_data

signal_config = config.get_config('signal')
signal_data = factor_scoring.generate_single_factor_signals(
    factor_data, 
    'production_score', 
    signal_config.get('signal_threshold', 0),
    signal_config.get('position_base', 0.5),
    signal_config.get('position_multiplier', 0.5)
)
signal_data

backtest_result = backtest_engine.run_single_factor_backtest(
    signal_data,
    'production_score',
    '000906.SH',
    signal_config.get('signal_threshold', 0),
    signal_config.get('position_base', 0.5),
    signal_config.get('position_multiplier', 0.5)
)

backtest_results = backtest_engine.run_backtest(signal_data, '000906.SH', mode='single')

# 获取基准数据
start_date = signal_data['date'].min().strftime('%Y-%m-%d')
end_date = signal_data['date'].max().strftime('%Y-%m-%d')
benchmark_data = backtest_engine.get_benchmark_data('000906.SH', start_date, end_date)


days=3
backtest_results[f'forward_return_{days}d'] = (
                backtest_results['close'].shift(-days) / backtest_results['close'] - 1
            )

# 检验信号有效性
effectiveness = backtest_engine.validate_signal_effectiveness(backtest_results, benchmark_data, 'production_score')
effectiveness

comprehensive_scores = factor_scoring.calculate_comprehensive_score(
            factors
        )

signals = factor_scoring.generate_trading_signals(
            comprehensive_scores
        )
signals

backtest_results = backtest_engine.run_backtest(
            signals, '000906.SH'
        )
backtest_results

benchmark_data = backtest_engine.get_benchmark_data(
            '000906.SH',
            '20220101',
            '20250704'
        )
benchmark_data

# 基本统计指标
index_code = backtest_results['code'].iloc[0]
daily_returns = backtest_results['daily_return'].dropna()

# 计算信息比率（相对基准的超额收益/跟踪误差）
information_ratio = 0
tracking_error = 0
excess_return = 0

if benchmark_data is not None and not benchmark_data.empty:
    # 合并数据计算超额收益
    merged_data = backtest_results.merge(benchmark_data[['date', 'benchmark_return']],
                                        on='date', how='inner')
    if not merged_data.empty:
        excess_returns = merged_data['daily_return'] - merged_data['benchmark_return']
        excess_return = (1 + excess_returns).cumprod().iloc[-1] ** (252 / len(excess_returns)) - 1 # 年化超额收益
        tracking_error = excess_returns.std() * np.sqrt(252)  # 年化跟踪误差

        if tracking_error != 0:
            information_ratio = excess_return / tracking_error

# 计算胜率（正收益交易日占比）
win_rate = (daily_returns > 0).sum() / len(daily_returns) if len(daily_returns) > 0 else 0

# 计算盈亏比
positive_returns = daily_returns[daily_returns > 0]
negative_returns = daily_returns[daily_returns < 0]

avg_positive = positive_returns.mean() if len(positive_returns) > 0 else 0
avg_negative = abs(negative_returns.mean()) if len(negative_returns) > 0 else 0
profit_loss_ratio = avg_positive / avg_negative if avg_negative != 0 else 0

# 计算最大回撤
cumulative_returns = (1 + daily_returns).cumprod()
rolling_max = cumulative_returns.expanding().max()
drawdowns = (cumulative_returns - rolling_max) / rolling_max
max_drawdown = abs(drawdowns.min())

# 计算年化收益率和波动率
total_return = cumulative_returns.iloc[-1] - 1 if len(cumulative_returns) > 0 else 0
trading_days = len(daily_returns)
annualized_return = (1 + total_return) ** (252 / trading_days) - 1 if trading_days > 0 else 0
annualized_volatility = daily_returns.std() * np.sqrt(252)

# 计算夏普比率
risk_free_rate = 0.02  # 假设无风险利率2%
sharpe_ratio = (annualized_return - risk_free_rate) / annualized_volatility if annualized_volatility != 0 else 0

# 计算信号相关指标
signal_stats = {}
if 'signal' in backtest_results.columns:
    signals = backtest_results['signal'].dropna()
    signal_stats = {
        'signal_count': len(signals),
        'buy_signals': (signals == 1).sum(),
        'sell_signals': (signals == -1).sum(),
        'hold_signals': (signals == 0).sum(),
        'signal_change_frequency': (signals.diff() != 0).sum() / len(signals) if len(signals) > 0 else 0
    }

# 因子得分相关指标
factor_stats = {}
if 'factor_score' in backtest_results.columns:
    factor_scores = backtest_results['factor_score'].dropna()
    factor_stats = {
        'factor_mean': factor_scores.mean(),
        'factor_std': factor_scores.std(),
        'factor_min': factor_scores.min(),
        'factor_max': factor_scores.max(),
        'factor_positive_ratio': (factor_scores > 0).sum() / len(factor_scores) if len(factor_scores) > 0 else 0
    }


sql = '''
select min(TRADE_DT) from aindexeodprices
where S_INFO_WINDCODE = '000906.SH'
'''
db.sql_to_df(sql)

import tushare as ts
ts.set_token('ece58a3a785f59873e795abd86f1c292fc488f4a191fbc18d657316a')
pro = ts.pro_api()

index_weights = []

import pandas as pd

for codes in ['000922.CSI', 'h30269.CSI', '930955.CSI', '000824.CSI', '000825.CSI', '931468.CSI', '930914.CSI', 'HSHYLV.HI', '931233.CSI', '931722.CSI', 'HSSCSOY.HI']:
    df1 = pro.index_weight(index_code=codes, start_date='20180101', end_date='20211231')
    index_weights.append(df1)
    df2 = pro.index_weight(index_code=codes, start_date='20220101', end_date='20250630')
    index_weights.append(df2)
index_weight = pd.concat(index_weights)

index_weight.groupby(['index_code', 'trade_date']).sum()

index_weight.to_csv('index_weight.csv')