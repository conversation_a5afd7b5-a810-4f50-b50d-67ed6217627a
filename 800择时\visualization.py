"""
结果可视化和指标计算模块
实现净值曲线绘制、年化收益率、波动率、回撤、夏普比率等指标计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


plt.style.use('ggplot')
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class Visualization:
    """可视化和指标计算类"""
    
    def __init__(self):
        """初始化可视化类"""
        self.figure_size = (12, 8)
        self.colors = {
            'strategy': '#1f77b4',
            'benchmark': '#ff7f0e',
            'drawdown': '#d62728',
            'positive': '#2ca02c',
            'negative': '#d62728'
        }
    
    def plot_nav_comparison(self, backtest_results, benchmark_data, title="策略与基准净值对比"):
        """
        绘制策略与基准的净值曲线对比图
        
        Args:
            backtest_results: 回测结果DataFrame
            benchmark_data: 基准数据DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 合并数据
        if not backtest_results.empty and not benchmark_data.empty:
            merged_data = backtest_results[['date', 'nav']].merge(
                benchmark_data[['date', 'benchmark_nav']], 
                on='date', how='inner'
            )
            
            # 绘制净值曲线
            ax.plot(merged_data['date'], merged_data['nav'], 
                   color=self.colors['strategy'], linewidth=2, label='策略净值')
            ax.plot(merged_data['date'], merged_data['benchmark_nav'], 
                   color=self.colors['benchmark'], linewidth=2, label='基准净值')
            
            # 设置图表格式
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('日期', fontsize=12)
            ax.set_ylabel('净值', fontsize=12)
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.xticks(rotation=45)
            
            # 添加统计信息
            strategy_return = (merged_data['nav'].iloc[-1] - 1) * 100
            benchmark_return = (merged_data['benchmark_nav'].iloc[-1] - 1) * 100
            excess_return = strategy_return - benchmark_return
            
            info_text = f'策略收益: {strategy_return:.2f}%\n基准收益: {benchmark_return:.2f}%\n超额收益: {excess_return:.2f}%'
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_drawdown(self, backtest_results, title="策略回撤分析"):
        """
        绘制回撤曲线
        
        Args:
            backtest_results: 回测结果DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figure_size, height_ratios=[2, 1])
        
        if not backtest_results.empty:
            # 计算回撤
            nav_series = backtest_results['nav']
            rolling_max = nav_series.expanding().max()
            drawdown = (nav_series - rolling_max) / rolling_max * 100
            
            # 绘制净值曲线
            ax1.plot(backtest_results['date'], nav_series, 
                    color=self.colors['strategy'], linewidth=2, label='策略净值')
            ax1.fill_between(backtest_results['date'], nav_series, rolling_max, 
                           where=(nav_series < rolling_max), color=self.colors['drawdown'], 
                           alpha=0.3, label='回撤区域')
            
            ax1.set_title(title, fontsize=16, fontweight='bold')
            ax1.set_ylabel('净值', fontsize=12)
            ax1.legend(fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 绘制回撤曲线
            ax2.fill_between(backtest_results['date'], drawdown, 0, 
                           color=self.colors['drawdown'], alpha=0.7)
            ax2.plot(backtest_results['date'], drawdown, 
                    color=self.colors['drawdown'], linewidth=1)
            
            ax2.set_xlabel('日期', fontsize=12)
            ax2.set_ylabel('回撤 (%)', fontsize=12)
            ax2.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            for ax in [ax1, ax2]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            
            plt.xticks(rotation=45)
            
            # 添加最大回撤信息
            max_drawdown = drawdown.min()
            max_dd_date = backtest_results.loc[drawdown.idxmin(), 'date']
            
            info_text = f'最大回撤: {max_drawdown:.2f}%\n发生日期: {max_dd_date.strftime("%Y-%m-%d")}'
            ax2.text(0.02, 0.02, info_text, transform=ax2.transAxes, 
                    verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_monthly_returns(self, backtest_results, title="月度收益率分布"):
        """
        绘制月度收益率分布
        
        Args:
            backtest_results: 回测结果DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figure_size)
        
        if not backtest_results.empty:
            # 计算月度收益率
            monthly_data = backtest_results.copy()
            monthly_data['date'] = pd.to_datetime(monthly_data['date'])
            monthly_data['year_month'] = monthly_data['date'].dt.to_period('M')
            
            monthly_returns = monthly_data.groupby('year_month')['nav'].last().pct_change().dropna() * 100
            
            # 绘制月度收益率柱状图
            colors = [self.colors['positive'] if x > 0 else self.colors['negative'] for x in monthly_returns]
            ax1.bar(range(len(monthly_returns)), monthly_returns, color=colors, alpha=0.7)
            ax1.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
            ax1.set_title('月度收益率', fontsize=14, fontweight='bold')
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('收益率 (%)', fontsize=12)
            ax1.grid(True, alpha=0.3)
            
            # 设置x轴标签
            if len(monthly_returns) > 12:
                step = len(monthly_returns) // 12
                ax1.set_xticks(range(0, len(monthly_returns), step))
                ax1.set_xticklabels([str(monthly_returns.index[i]) for i in range(0, len(monthly_returns), step)], 
                                  rotation=45)
            else:
                ax1.set_xticks(range(len(monthly_returns)))
                ax1.set_xticklabels([str(x) for x in monthly_returns.index], rotation=45)
            
            # 绘制月度收益率分布直方图
            ax2.hist(monthly_returns, bins=20, color=self.colors['strategy'], alpha=0.7, edgecolor='black')
            ax2.axvline(x=monthly_returns.mean(), color='red', linestyle='--', linewidth=2, label=f'均值: {monthly_returns.mean():.2f}%')
            ax2.set_title('月度收益率分布', fontsize=14, fontweight='bold')
            ax2.set_xlabel('收益率 (%)', fontsize=12)
            ax2.set_ylabel('频次', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 添加统计信息
            positive_months = (monthly_returns > 0).sum()
            total_months = len(monthly_returns)
            win_rate = positive_months / total_months * 100
            
            stats_text = f'正收益月份: {positive_months}/{total_months}\n胜率: {win_rate:.1f}%\n平均收益: {monthly_returns.mean():.2f}%\n收益标准差: {monthly_returns.std():.2f}%'
            ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_factor_scores(self, factor_scores, title="因子得分时序图"):
        """
        绘制因子得分时序图
        
        Args:
            factor_scores: 因子得分DataFrame
            title: 图表标题
            
        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        if not factor_scores.empty:
            score_cols = ['macro_score', 'meso_score', 'micro_score', 'comprehensive_score']
            score_names = ['宏观经济得分', '中观市场得分', '微观标的得分', '综合因子得分']
            score_map = {'macro': '宏观经济得分', 'meso': '中观市场得分', 'micro': '微观标的得分',
                        'comprehensive': '综合因子得分', 'production': '生产因子得分',
                        'consumption': '消费因子得分', 'investment': '投资因子得分',
                        'monetary': '货币因子得分', 'exchange_rate': '汇率因子得分',
                        'bond': '国债因子得分', 'credit': '信用因子得分',
                        'margin_trading': '两融因子得分', 'turnover_trend': '换手率因子得分',
                        'market_money_flow': '资金流因子得分', 'main_money_flow': '主力资金流因子得分',
                        'vix': '恐慌指数因子得分', 'value': '价值因子得分', 'momentum': '动量因子得分',
                        'roe': '盈利因子得分', 'liquidity': '流动性因子得分', 'bias': 'bias因子得分',
                        'aroon': 'aroon因子得分', 'adx': 'adx因子得分', 'uo': 'uo因子得分',
                        'atr': 'atr因子得分', 'udvd': 'udvd因子得分', 'new_high_low': '新高新低因子得分'}
            
            for i, (col, name) in enumerate(zip(score_cols, score_names)):
                if col in factor_scores.columns:
                    ax = axes[i]
                    ax.plot(factor_scores['date'], factor_scores[col], 
                           linewidth=1.5, label=name)
                    ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
                    ax.fill_between(factor_scores['date'], factor_scores[col], 0, 
                                  where=(factor_scores[col] > 0), color=self.colors['positive'], alpha=0.3)
                    ax.fill_between(factor_scores['date'], factor_scores[col], 0, 
                                  where=(factor_scores[col] < 0), color=self.colors['negative'], alpha=0.3)
                    
                    ax.set_title(name, fontsize=12, fontweight='bold')
                    ax.set_ylabel('得分', fontsize=10)
                    ax.grid(True, alpha=0.3)
                    
                    # 格式化x轴
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig
    
    def create_performance_report(self, strategy_metrics, benchmark_metrics=None, excess_metrics=None):
        """
        创建业绩报告表格（参考价值成长项目格式）

        Args:
            strategy_metrics: 策略业绩指标字典
            benchmark_metrics: 基准业绩指标字典
            excess_metrics: 超额业绩指标字典

        Returns:
            pandas.DataFrame: 业绩报告表格
        """
        if not strategy_metrics:
            return pd.DataFrame()

        # 创建报告数据
        report_data = {
            '指标': [
                '总收益率 (%)',
                '年化收益率 (%)',
                '年化波动率 (%)',
                '夏普比率',
                '最大回撤 (%)',
                '胜率 (%)',
                '盈亏比',
                '交易天数'
            ],
            '择时策略': [
                f"{strategy_metrics.get('total_return', 0):.2%}",
                f"{strategy_metrics.get('annualized_return', 0):.2%}",
                f"{strategy_metrics.get('annualized_volatility', 0):.2%}",
                f"{strategy_metrics.get('sharpe_ratio', 0):.3f}",
                f"{abs(strategy_metrics.get('max_drawdown', 0)):.2%}",
                f"{strategy_metrics.get('win_rate', 0):.2%}",
                f"{strategy_metrics.get('profit_loss_ratio', 0):.3f}",
                f"{strategy_metrics.get('trading_days', 0):.0f}"
            ]
        }

        if benchmark_metrics:
            report_data['基准指数'] = [
                f"{benchmark_metrics.get('total_return', 0):.2%}",
                f"{benchmark_metrics.get('annualized_return', 0):.2%}",
                f"{benchmark_metrics.get('annualized_volatility', 0):.2%}",
                f"{benchmark_metrics.get('sharpe_ratio', 0):.3f}",
                f"{abs(benchmark_metrics.get('max_drawdown', 0)):.2%}",
                f"{benchmark_metrics.get('win_rate', 0):.2%}",
                f"{benchmark_metrics.get('profit_loss_ratio', 0):.3f}",
                f"{benchmark_metrics.get('trading_days', 0):.0f}"
            ]

        if excess_metrics:
            report_data['超额业绩'] = [
                f"{excess_metrics.get('total_return', 0):.2%}",
                f"{excess_metrics.get('annualized_return', 0):.2%}",
                f"{excess_metrics.get('annualized_volatility', 0):.2%}",
                f"{excess_metrics.get('sharpe_ratio', 0):.3f}",
                f"{abs(excess_metrics.get('max_drawdown', 0)):.2%}",
                f"{excess_metrics.get('win_rate', 0):.2%}",
                f"{excess_metrics.get('profit_loss_ratio', 0):.3f}",
                f"{excess_metrics.get('trading_days', 0):.0f}"
            ]

        return pd.DataFrame(report_data)
    
    def save_all_charts(self, backtest_results, benchmark_data, factor_scores, 
                       output_dir='./charts/', prefix='strategy'):
        """
        保存所有图表
        
        Args:
            backtest_results: 回测结果
            benchmark_data: 基准数据
            factor_scores: 因子得分
            output_dir: 输出目录
            prefix: 文件名前缀
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 策略、基准、相对表现走势图
        fig1 = self.plot_strategy_benchmark_relative_performance(backtest_results, benchmark_data)
        fig1.savefig(f'{output_dir}/{prefix}_strategy_benchmark_relative.png', dpi=300, bbox_inches='tight')
        plt.close(fig1)

        # 2. 净值对比图
        fig2 = self.plot_nav_comparison(backtest_results, benchmark_data)
        fig2.savefig(f'{output_dir}/{prefix}_nav_comparison.png', dpi=300, bbox_inches='tight')
        plt.close(fig2)

        # 3. 回撤分析图
        fig3 = self.plot_drawdown(backtest_results)
        fig3.savefig(f'{output_dir}/{prefix}_drawdown.png', dpi=300, bbox_inches='tight')
        plt.close(fig3)

        # 4. 月度收益率图
        fig4 = self.plot_monthly_returns(backtest_results)
        fig4.savefig(f'{output_dir}/{prefix}_monthly_returns.png', dpi=300, bbox_inches='tight')
        plt.close(fig4)

        # 5. 因子得分图
        if not factor_scores.empty:
            fig5 = self.plot_factor_scores(factor_scores)
            fig5.savefig(f'{output_dir}/{prefix}_factor_scores.png', dpi=300, bbox_inches='tight')
            plt.close(fig5)

        print(f"所有图表已保存到 {output_dir} 目录")

    def plot_strategy_benchmark_relative_performance(self, backtest_results, benchmark_data,
                                                   title="策略、基准、相对表现走势图"):
        """
        绘制策略、基准、相对表现的走势图

        Args:
            backtest_results: 回测结果DataFrame
            benchmark_data: 基准数据DataFrame
            title: 图表标题

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10),
                                      gridspec_kw={'height_ratios': [2, 1]})

        # 合并数据
        if not backtest_results.empty and not benchmark_data.empty:
            merged_data = backtest_results.merge(
                benchmark_data[['date', 'benchmark_nav']],
                on='date', how='inner'
            )
            merged_data = merged_data.sort_values('date')

            if not merged_data.empty:
                # 上图：策略净值和基准净值
                ax1.plot(merged_data['date'], merged_data['nav'],
                        color=self.colors['strategy'], linewidth=2, label='策略净值')
                ax1.plot(merged_data['date'], merged_data['benchmark_nav'],
                        color=self.colors['benchmark'], linewidth=2, label='基准净值')

                ax1.set_title('策略与基准净值走势', fontsize=14, fontweight='bold')
                ax1.set_ylabel('净值')
                ax1.legend()
                ax1.grid(True, alpha=0.3)

                # 下图：相对表现（超额收益）
                relative_performance = (merged_data['nav'] / merged_data['benchmark_nav'] - 1) * 100
                ax2.plot(merged_data['date'], relative_performance,
                        color='purple', linewidth=2, label='相对表现')
                ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                ax2.fill_between(merged_data['date'], 0, relative_performance,
                               where=(relative_performance >= 0),
                               color=self.colors['positive'], alpha=0.3, label='超额收益')
                ax2.fill_between(merged_data['date'], 0, relative_performance,
                               where=(relative_performance < 0),
                               color=self.colors['negative'], alpha=0.3, label='超额损失')

                ax2.set_title('策略相对基准表现', fontsize=14, fontweight='bold')
                ax2.set_xlabel('日期')
                ax2.set_ylabel('相对表现 (%)')
                ax2.legend()
                ax2.grid(True, alpha=0.3)

                # 格式化x轴
                for ax in [ax1, ax2]:
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig

    def plot_factor_with_benchmark(self, factor_data, benchmark_data, factor_name,
                                  factor_score_col=None, title=None):
        """
        绘制因子值/得分与基准走势对比图

        Args:
            factor_data: 包含因子数据的DataFrame
            benchmark_data: 基准数据DataFrame
            factor_name: 因子名称
            factor_score_col: 因子得分列名，如果为None则使用factor_name
            title: 图表标题

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        if factor_score_col is None:
            factor_score_col = factor_name

        if title is None:
            title = f"{factor_name}因子与基准走势对比"

        # 创建子图
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12),
                                           gridspec_kw={'height_ratios': [2, 1, 1]})

        # 合并数据
        merged_data = factor_data.merge(benchmark_data[['date', 'close', 'benchmark_nav']],
                                      on='date', how='inner')
        merged_data = merged_data.sort_values('date')

        if merged_data.empty:
            print("警告：无法合并因子数据和基准数据")
            return fig

        # 第一个子图：基准价格走势
        ax1.plot(merged_data['date'], merged_data['close'],
                color=self.colors['benchmark'], linewidth=2, label='中证800指数')
        ax1.set_title(f"{title} - 基准走势", fontsize=14, fontweight='bold')
        ax1.set_ylabel('指数点位', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 第二个子图：因子原始值（如果存在）
        if factor_name in merged_data.columns and factor_name != factor_score_col:
            ax2.plot(merged_data['date'], merged_data[factor_name],
                    color=self.colors['strategy'], linewidth=1.5, label=f'{factor_name}原始值')
            ax2.set_title(f"{factor_name}原始值", fontsize=12)
            ax2.set_ylabel('因子值', fontsize=10)
            ax2.legend(loc='upper left')
            ax2.grid(True, alpha=0.3)
        else:
            # 如果没有原始值，显示基准净值
            ax2.plot(merged_data['date'], merged_data['benchmark_nav'],
                    color=self.colors['benchmark'], linewidth=1.5, label='基准净值')
            ax2.set_title("基准净值走势", fontsize=12)
            ax2.set_ylabel('净值', fontsize=10)
            ax2.legend(loc='upper left')
            ax2.grid(True, alpha=0.3)

        # 第三个子图：因子得分（带阴影）
        if factor_score_col in merged_data.columns:
            factor_scores = merged_data[factor_score_col]

            # 绘制因子得分线
            ax3.plot(merged_data['date'], factor_scores,
                    color='black', linewidth=1.5, label=f'{factor_score_col}')

            # 添加阴影：正值为绿色，负值为红色
            positive_mask = factor_scores > 0
            negative_mask = factor_scores < 0

            # 正值阴影（绿色）
            ax3.fill_between(merged_data['date'], 0, factor_scores,
                           where=positive_mask, color=self.colors['positive'],
                           alpha=0.3, label='看多信号')

            # 负值阴影（红色）
            ax3.fill_between(merged_data['date'], 0, factor_scores,
                           where=negative_mask, color=self.colors['negative'],
                           alpha=0.3, label='看空信号')

            # 添加零线
            ax3.axhline(y=0, color='gray', linestyle='--', alpha=0.5)

            # 如果因子得分主要是1/-1，添加参考线
            unique_scores = factor_scores.dropna().unique()
            if len(unique_scores) <= 5 and all(abs(score) <= 1.1 for score in unique_scores):
                ax3.axhline(y=1, color='green', linestyle=':', alpha=0.7, label='强看多')
                ax3.axhline(y=-1, color='red', linestyle=':', alpha=0.7, label='强看空')
                ax3.set_ylim(-1.2, 1.2)

            ax3.set_title(f"{factor_score_col}得分", fontsize=12)
            ax3.set_ylabel('因子得分', fontsize=10)
            ax3.set_xlabel('日期', fontsize=10)
            ax3.legend(loc='upper left')
            ax3.grid(True, alpha=0.3)

        # 格式化x轴日期
        for ax in [ax1, ax2, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()
        return fig

    def plot_factor_effectiveness_comparison(self, effectiveness_df, title="因子有效性比较"):
        """
        绘制因子有效性比较图

        Args:
            effectiveness_df: 包含因子有效性指标的DataFrame
            title: 图表标题

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        if effectiveness_df.empty:
            print("警告：有效性数据为空")
            return plt.figure()

        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 夏普比率比较
        if 'sharpe_ratio' in effectiveness_df.columns:
            effectiveness_df_sorted = effectiveness_df.sort_values('sharpe_ratio', ascending=True)
            bars1 = ax1.barh(effectiveness_df_sorted['factor_name'], effectiveness_df_sorted['sharpe_ratio'])
            ax1.set_title('夏普比率比较', fontsize=14, fontweight='bold')
            ax1.set_xlabel('夏普比率')
            ax1.grid(True, alpha=0.3)

            # 颜色编码：正值绿色，负值红色
            for i, bar in enumerate(bars1):
                if effectiveness_df_sorted['sharpe_ratio'].iloc[i] >= 0:
                    bar.set_color(self.colors['positive'])
                else:
                    bar.set_color(self.colors['negative'])

        # 2. 年化收益率比较
        if 'annualized_return' in effectiveness_df.columns:
            effectiveness_df_sorted = effectiveness_df.sort_values('annualized_return', ascending=True)
            bars2 = ax2.barh(effectiveness_df_sorted['factor_name'],
                           effectiveness_df_sorted['annualized_return'] * 100)
            ax2.set_title('年化收益率比较', fontsize=14, fontweight='bold')
            ax2.set_xlabel('年化收益率 (%)')
            ax2.grid(True, alpha=0.3)

            # 颜色编码
            for i, bar in enumerate(bars2):
                if effectiveness_df_sorted['annualized_return'].iloc[i] >= 0:
                    bar.set_color(self.colors['positive'])
                else:
                    bar.set_color(self.colors['negative'])

        # 3. 信息比率比较
        if 'information_ratio' in effectiveness_df.columns:
            effectiveness_df_sorted = effectiveness_df.sort_values('information_ratio', ascending=True)
            bars3 = ax3.barh(effectiveness_df_sorted['factor_name'], effectiveness_df_sorted['information_ratio'])
            ax3.set_title('信息比率比较', fontsize=14, fontweight='bold')
            ax3.set_xlabel('信息比率')
            ax3.grid(True, alpha=0.3)

            # 颜色编码
            for i, bar in enumerate(bars3):
                if effectiveness_df_sorted['information_ratio'].iloc[i] >= 0:
                    bar.set_color(self.colors['positive'])
                else:
                    bar.set_color(self.colors['negative'])

        # 4. 最大回撤比较
        if 'max_drawdown' in effectiveness_df.columns:
            effectiveness_df_sorted = effectiveness_df.sort_values('max_drawdown', ascending=False)
            bars4 = ax4.barh(effectiveness_df_sorted['factor_name'],
                           effectiveness_df_sorted['max_drawdown'] * 100)
            ax4.set_title('最大回撤比较', fontsize=14, fontweight='bold')
            ax4.set_xlabel('最大回撤 (%)')
            ax4.grid(True, alpha=0.3)

            # 回撤都用红色表示
            for bar in bars4:
                bar.set_color(self.colors['negative'])

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig

    def plot_single_factor_analysis(self, factor_data, benchmark_data, backtest_results,
                                   factor_name, factor_score_col=None):
        """
        绘制单因子完整分析图表

        Args:
            factor_data: 因子数据DataFrame
            benchmark_data: 基准数据DataFrame
            backtest_results: 回测结果DataFrame
            factor_name: 因子名称
            factor_score_col: 因子得分列名

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        if factor_score_col is None:
            factor_score_col = factor_name

        # 创建大图
        fig = plt.figure(figsize=(16, 20))

        # 1. 因子与基准对比（占用上半部分）
        ax1 = plt.subplot2grid((4, 2), (0, 0), colspan=2, rowspan=2)

        # 合并数据
        merged_data = factor_data.merge(benchmark_data[['date', 'close']], on='date', how='inner')
        merged_data = merged_data.sort_values('date')

        if not merged_data.empty:
            # 双y轴图
            ax1_twin = ax1.twinx()

            # 左轴：基准价格
            ax1.plot(merged_data['date'], merged_data['close'],
                           color=self.colors['benchmark'], linewidth=2, label='中证800指数')
            ax1.set_ylabel('指数点位', color=self.colors['benchmark'], fontsize=12)
            ax1.tick_params(axis='y', labelcolor=self.colors['benchmark'])

            # 右轴：因子得分
            if factor_score_col in merged_data.columns:
                factor_scores = merged_data[factor_score_col]
                ax1_twin.plot(merged_data['date'], factor_scores,
                                    color='black', linewidth=1.5, label=f'{factor_score_col}')

                # 添加阴影
                positive_mask = factor_scores > 0
                negative_mask = factor_scores < 0

                ax1_twin.fill_between(merged_data['date'], 0, factor_scores,
                                    where=positive_mask, color=self.colors['positive'],
                                    alpha=0.3, label='看多信号')
                ax1_twin.fill_between(merged_data['date'], 0, factor_scores,
                                    where=negative_mask, color=self.colors['negative'],
                                    alpha=0.3, label='看空信号')

                ax1_twin.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
                ax1_twin.set_ylabel('因子得分', color='black', fontsize=12)
                ax1_twin.tick_params(axis='y', labelcolor='black')

            ax1.set_title(f'{factor_name}因子与基准走势对比', fontsize=16, fontweight='bold')

            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax1_twin.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        # 2. 策略净值曲线（左下）
        ax2 = plt.subplot2grid((4, 2), (2, 0))
        if not backtest_results.empty and 'cumulative_return' in backtest_results.columns:
            nav_data = (1 + backtest_results['cumulative_return'])
            ax2.plot(backtest_results['date'], nav_data,
                    color=self.colors['strategy'], linewidth=2, label='策略净值')
            ax2.set_title('策略净值曲线', fontsize=12, fontweight='bold')
            ax2.set_ylabel('净值')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        # 3. 回撤曲线（右下）
        ax3 = plt.subplot2grid((4, 2), (2, 1))
        if not backtest_results.empty and 'cumulative_return' in backtest_results.columns:
            cumulative_returns = backtest_results['cumulative_return']
            rolling_max = cumulative_returns.expanding().max()
            drawdowns = (cumulative_returns - rolling_max) / (1 + rolling_max)

            ax3.fill_between(backtest_results['date'], 0, drawdowns * 100,
                           color=self.colors['drawdown'], alpha=0.7)
            ax3.set_title('策略回撤', fontsize=12, fontweight='bold')
            ax3.set_ylabel('回撤 (%)')
            ax3.grid(True, alpha=0.3)

        # 4. 因子统计信息（底部）
        ax4 = plt.subplot2grid((4, 2), (3, 0), colspan=2)
        ax4.axis('off')

        # 添加统计信息文本
        if not merged_data.empty and factor_score_col in merged_data.columns:
            factor_scores = merged_data[factor_score_col].dropna()
            stats_text = f"""
因子统计信息：
• 因子均值: {factor_scores.mean():.4f}
• 因子标准差: {factor_scores.std():.4f}
• 因子最大值: {factor_scores.max():.4f}
• 因子最小值: {factor_scores.min():.4f}
• 正值占比: {(factor_scores > 0).mean():.2%}
• 信号变化频率: {(factor_scores.diff() != 0).mean():.2%}
            """
            ax4.text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

        plt.tight_layout()
        return fig

    def calculate_annual_performance(self, backtest_results, benchmark_data=None,
                                   factor_data=None, signals=None,
                                   backtest_engine=None,
                                   output_dir='./results/', file_prefix='strategy'):
        """
        计算年度业绩指标统计（参考价值成长项目）

        Args:
            backtest_results: 回测结果DataFrame
            benchmark_data: 基准数据DataFrame
            factor_data: 因子数据DataFrame
            signals: 信号数据DataFrame
            backtest_engine: 回测引擎实例
            output_dir: 输出目录
            file_prefix: 文件前缀

        Returns:
            pandas.DataFrame: 年度业绩统计表
        """
        if backtest_results.empty or 'nav' not in backtest_results.columns:
            print("警告：回测结果数据为空或缺少必要列")
            return pd.DataFrame()

        # 确保日期列为datetime类型
        backtest_results_copy = backtest_results.copy()
        if 'date' in backtest_results_copy.columns:
            backtest_results_copy['date'] = pd.to_datetime(backtest_results_copy['date'])
            backtest_results_copy = backtest_results_copy.set_index('date')

        # 准备基准数据
        benchmark_nav = None
        benchmark_returns = None
        if benchmark_data is not None and not benchmark_data.empty:
            benchmark_data_copy = benchmark_data.copy()
            if 'date' in benchmark_data_copy.columns:
                benchmark_data_copy['date'] = pd.to_datetime(benchmark_data_copy['date'])
                benchmark_data_copy = benchmark_data_copy.set_index('date')

            # 计算基准净值和收益率
            if 'benchmark_nav' in benchmark_data_copy.columns:
                benchmark_nav = benchmark_data_copy['benchmark_nav']
            elif 'nav' in benchmark_data_copy.columns:
                benchmark_nav = benchmark_data_copy['nav']

            if benchmark_nav is not None:
                benchmark_returns = benchmark_nav.pct_change()

        # 按年分组计算业绩
        annual_stats = []

        # 获取所有年份
        years = sorted(backtest_results_copy.index.year.unique())

        for year in years:
            year_data = backtest_results_copy[backtest_results_copy.index.year == year]

            if len(year_data) < 2:
                continue

            # 策略年度收益率
            start_nav = year_data['nav'].iloc[0]
            end_nav = year_data['nav'].iloc[-1]
            strategy_return = (end_nav / start_nav - 1) * 100

            # 基准年度收益率
            benchmark_return = np.nan
            if benchmark_nav is not None:
                year_benchmark = benchmark_nav[benchmark_nav.index.year == year]
                if len(year_benchmark) >= 2:
                    start_benchmark = year_benchmark.iloc[0]
                    end_benchmark = year_benchmark.iloc[-1]
                    benchmark_return = (end_benchmark / start_benchmark - 1) * 100

            # 超额收益
            excess_return = strategy_return - benchmark_return if not np.isnan(benchmark_return) else np.nan

            # 年度波动率
            year_returns = year_data['daily_return'].dropna()
            volatility = year_returns.std() * np.sqrt(252) * 100 if len(year_returns) > 1 else np.nan

            # 年度最大回撤
            year_nav = year_data['nav']
            cumulative_max = year_nav.cummax()
            drawdown = (year_nav - cumulative_max) / cumulative_max
            max_drawdown = drawdown.min() * 100

            # 年度夏普比率
            risk_free_rate = 2.0  # 假设无风险利率2%
            sharpe_ratio = (strategy_return - risk_free_rate) / volatility if not np.isnan(volatility) and volatility > 0 else np.nan

            # 当年月度胜率（基于月度收益率）
            monthly_return = year_returns.resample('ME').apply(
                lambda x: (x + 1).prod() - 1
            )

            # 月度基准收益率
            monthly_benchmark_return = pd.Series(dtype=float)
            if benchmark_returns is not None:
                monthly_benchmark_return = benchmark_returns[benchmark_returns.index.year == year].resample('ME').apply(
                    lambda x: (x + 1).prod() - 1
                )

            # 月度超额收益和胜率
            if len(monthly_benchmark_return) > 0:
                monthly_excess_return = monthly_return - monthly_benchmark_return
                win_rate = (monthly_excess_return > 0).mean() * 100 if len(monthly_excess_return) > 0 else np.nan
            else:
                win_rate = (monthly_return > 0).mean() * 100 if len(monthly_return) > 0 else np.nan
                monthly_excess_return = monthly_return

            # 当年月度赔率
            if len(monthly_excess_return) > 0:
                positive_returns = monthly_excess_return[monthly_excess_return > 0]
                negative_returns = monthly_excess_return[monthly_excess_return < 0]
                if len(positive_returns) > 0 and len(negative_returns) > 0:
                    profit_loss_ratio = positive_returns.mean() / abs(negative_returns.mean())
                else:
                    profit_loss_ratio = np.nan
            else:
                profit_loss_ratio = np.nan

            annual_stats.append({
                '年份': year,
                '组合收益(%)': round(strategy_return, 2),
                '基准收益(%)': round(benchmark_return, 2) if not np.isnan(benchmark_return) else '--',
                '超额收益(%)': round(excess_return, 2) if not np.isnan(excess_return) else '--',
                '年化波动率(%)': round(volatility, 2) if not np.isnan(volatility) else '--',
                '最大回撤(%)': round(max_drawdown, 2),
                '夏普比率': round(sharpe_ratio, 2) if not np.isnan(sharpe_ratio) else '--',
                '胜率(%)': round(win_rate, 2) if not np.isnan(win_rate) else '--',
                '赔率': round(profit_loss_ratio, 2) if not np.isnan(profit_loss_ratio) else '--'
            })

        # 创建DataFrame
        annual_df = pd.DataFrame(annual_stats)

        # 计算全样本统计指标（参考价值成长项目）
        if len(backtest_results_copy) >= 2:
            # 计算总的交易天数和年数
            trading_days = len(backtest_results_copy)
            years_total = trading_days / 252

            # 全样本年化收益率（策略）
            total_nav_ratio = backtest_results_copy['nav'].iloc[-1] / backtest_results_copy['nav'].iloc[0]
            annualized_strategy_return = ((total_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

            # 全样本年化收益率（基准）
            annualized_benchmark_return = np.nan
            if benchmark_nav is not None and len(benchmark_nav) >= 2:
                benchmark_nav_ratio = benchmark_nav.iloc[-1] / benchmark_nav.iloc[0]
                annualized_benchmark_return = ((benchmark_nav_ratio) ** (1/years_total) - 1) * 100 if years_total > 0 else np.nan

            # 全样本年化超额收益
            annualized_excess_return = annualized_strategy_return - annualized_benchmark_return if not np.isnan(annualized_benchmark_return) else np.nan

            # 全样本年化波动率
            all_returns = backtest_results_copy['daily_return'].dropna()
            total_volatility = all_returns.std() * np.sqrt(252) * 100 if len(all_returns) > 1 else np.nan

            # 全样本最大回撤
            cumulative_max = backtest_results_copy['nav'].cummax()
            drawdown = (backtest_results_copy['nav'] - cumulative_max) / cumulative_max
            total_max_drawdown = drawdown.min() * 100

            # 全样本夏普比率
            total_sharpe_ratio = (annualized_strategy_return - 2.0) / total_volatility if not np.isnan(total_volatility) and total_volatility > 0 else np.nan

            # 全样本胜率
            monthly_return = all_returns.resample('ME').apply(
                lambda x: (x + 1).prod() - 1
            )

            if benchmark_returns is not None:
                monthly_benchmark_return = benchmark_returns.resample('ME').apply(
                    lambda x: (x + 1).prod() - 1
                )
                monthly_excess_return = monthly_return - monthly_benchmark_return
                total_win_rate = (monthly_excess_return > 0).mean() * 100 if len(monthly_excess_return) > 0 else np.nan
            else:
                total_win_rate = (monthly_return > 0).mean() * 100 if len(monthly_return) > 0 else np.nan
                monthly_excess_return = monthly_return

            # 全样本赔率
            if len(monthly_excess_return) > 0:
                positive_returns = monthly_excess_return[monthly_excess_return > 0]
                negative_returns = monthly_excess_return[monthly_excess_return < 0]
                if len(positive_returns) > 0 and len(negative_returns) > 0:
                    total_profit_loss_ratio = positive_returns.mean() / abs(negative_returns.mean())
                else:
                    total_profit_loss_ratio = np.nan
            else:
                total_profit_loss_ratio = np.nan

            # 添加全样本统计行
            total_stats = {
                '年份': '全样本',
                '组合收益(%)': round(annualized_strategy_return, 2) if not np.isnan(annualized_strategy_return) else '--',
                '基准收益(%)': round(annualized_benchmark_return, 2) if not np.isnan(annualized_benchmark_return) else '--',
                '超额收益(%)': round(annualized_excess_return, 2) if not np.isnan(annualized_excess_return) else '--',
                '年化波动率(%)': round(total_volatility, 2) if not np.isnan(total_volatility) else '--',
                '最大回撤(%)': round(total_max_drawdown, 2),
                '夏普比率': round(total_sharpe_ratio, 2) if not np.isnan(total_sharpe_ratio) else '--',
                '胜率(%)': round(total_win_rate, 2) if not np.isnan(total_win_rate) else '--',
                '赔率': round(total_profit_loss_ratio, 2) if not np.isnan(total_profit_loss_ratio) else '--'
            }

            # 将全样本统计添加到DataFrame
            annual_df = pd.concat([annual_df, pd.DataFrame([total_stats])], ignore_index=True)

        # 保存年度业绩统计
        if not annual_df.empty:
            output_file = f"{output_dir}/{file_prefix}_annual_performance.csv"
            annual_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"年度业绩统计已保存至: {output_file}")

        return annual_df
