{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7cf743d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-09-01 08:42:39,298 - INFO - ============================================================\n", "2025-09-01 08:42:39,298 - INFO - 大小盘轮动策略系统\n", "2025-09-01 08:42:39,299 - INFO - ============================================================\n", "2025-09-01 08:42:39,300 - INFO - 正在初始化数据库连接...\n", "2025-09-01 08:42:39,335 - INFO - 正在初始化因子计算模块...\n", "2025-09-01 08:42:39,337 - INFO - 使用myfactors模块\n", "2025-09-01 08:42:39,337 - INFO - 正在初始化回测引擎...\n", "2025-09-01 08:42:39,338 - INFO - 正在初始化可视化模块...\n", "2025-09-01 08:42:39,339 - INFO - 初始化完成！\n", "\n", "2025-09-01 08:42:39,339 - INFO - 开始运行大小盘轮动综合策略...\n", "2025-09-01 08:42:39,340 - INFO - 回测期间: 20170101 至 20250831\n", "2025-09-01 08:42:39,340 - INFO - 大盘指数: 000300.SH\n", "2025-09-01 08:42:39,341 - INFO - 小盘指数: 000852.SH\n", "2025-09-01 08:42:39,343 - INFO - 调仓频率: monthly\n", "2025-09-01 08:42:39,344 - INFO - 第一步：计算因子得分\n", "2025-09-01 08:42:39,344 - INFO - 开始计算大小盘轮动因子...\n", "2025-09-01 08:42:39,389 - INFO - 计算bond_yield_factor...\n", "2025-09-01 08:42:39,390 - INFO - 短期均线窗口大小为：20天\n", "2025-09-01 08:42:39,391 - INFO - 长期均线窗口大小为：240天\n"]}, {"name": "stdout", "output_type": "stream", "text": ["数据库连接成功\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-01 08:42:43,794 - INFO - 国债收益率短期均线高于长期均线时，看多大盘，低于长期均线时看空大盘\n", "2025-09-01 08:42:43,799 - INFO - 计算gold_factor...\n", "2025-09-01 08:42:43,800 - INFO - 黄金短期均线窗口大小为：20天\n", "2025-09-01 08:42:43,800 - INFO - 黄金长期均线窗口大小为：240天\n", "2025-09-01 08:42:47,795 - INFO - 黄金日收益率短期均线低于长期均线时看空大盘，高于长期均线时看多大盘\n", "2025-09-01 08:42:47,801 - INFO - 计算financing_balance_factor...\n", "2025-09-01 08:42:47,802 - INFO - 回看窗口大小为：240天\n", "2025-09-01 08:42:48,199 - INFO - 两融余额过去一年分位点高于50时看多大盘风格，低于50时看空大盘风格\n", "2025-09-01 08:42:48,203 - INFO - 计算turnover_factor...\n", "2025-09-01 08:42:48,204 - INFO - 短期窗口大小为：20天，长期窗口大小为：240天\n", "2025-09-01 08:42:48,569 - INFO - 短期换手率低于长期时看多大盘，高于长期时看空大盘\n", "2025-09-01 08:42:48,575 - INFO - 计算fixed_asset_investment_factor...\n", "2025-09-01 08:42:48,575 - INFO - 制造业固定资产投资完成额环比窗口大小为：3月\n", "2025-09-01 08:42:48,867 - INFO - 制造业固定资产投资完成额累计同比环比上升时看空大盘，下降时看多大盘\n", "2025-09-01 08:42:48,872 - INFO - 计算inflation_factor...\n", "2025-09-01 08:42:48,873 - INFO - 通胀因子环比窗口大小为：3月\n", "2025-09-01 08:42:49,112 - INFO - PPI同比平滑值环比上升时看多大盘，下降时看空大盘\n", "2025-09-01 08:42:49,117 - INFO - 计算m1_m2_factor...\n", "2025-09-01 08:42:49,118 - INFO - M1-M2剪刀差因子环比窗口大小为：3月\n", "2025-09-01 08:42:49,356 - INFO - M1同比平滑值环比下降即货币紧缩时看多大盘，上升即货币宽松时看空大盘\n", "2025-09-01 08:42:49,360 - INFO - 计算credit_spread_factor...\n", "2025-09-01 08:42:49,361 - INFO - 信用利差因子环比窗口大小为：3月\n", "2025-09-01 08:42:53,288 - INFO - 信用利差短期均线高于长期均线时看多大盘\n", "2025-09-01 08:42:53,294 - INFO - 计算price_factor...\n", "2025-09-01 08:42:53,294 - INFO - 估值因子分位数窗口大小为：240，平滑窗口大小为：20月\n", "2025-09-01 08:42:54,937 - INFO - 当大盘估值分位点高于小盘时看多大盘风格，反之看空大盘风格\n", "2025-09-01 08:42:55,259 - INFO - 当大盘价格分位点高于小盘时看多大盘风格，反之看空大盘风格\n", "2025-09-01 08:42:55,267 - INFO - 因子计算完成，共计算9个因子\n", "2025-09-01 08:42:55,269 - INFO - 因子数据计算完成，共 2104 个交易日\n", "2025-09-01 08:42:55,270 - INFO - 第二步：生成交易信号\n", "2025-09-01 08:42:55,595 - INFO - 交易信号生成完成，共 105 个调仓日\n", "2025-09-01 08:42:55,596 - INFO - 第三步：运行策略回测\n", "2025-09-01 08:42:56,911 - INFO - 第四步：计算基准表现\n", "2025-09-01 08:42:57,421 - INFO - 第五步：计算业绩指标\n", "2025-09-01 08:42:57,440 - INFO - 第六步：分析信号有效性\n", "2025-09-01 08:42:57,846 - INFO - 第七步：生成分析报告\n", "2025-09-01 08:42:57,848 - INFO - \n", "==================================================\n", "2025-09-01 08:42:57,849 - INFO - 策略业绩报告\n", "2025-09-01 08:42:57,850 - INFO - ==================================================\n", "2025-09-01 08:42:57,853 - INFO -        指标 大小盘轮动策略   基准指数    超额业绩\n", " 总收益率 (%) 134.71% 10.79% 114.77%\n", "年化收益率 (%)  10.76%  1.23%   9.59%\n", "年化波动率 (%)  22.27% 20.14%   7.62%\n", "     夏普比率   0.393 -0.038   0.995\n", " 最大回撤 (%)  37.36% 41.49%  10.13%\n", "   胜率 (%)  54.81% 51.92%  58.65%\n", "      盈亏比   1.303  1.051   1.738\n", "     交易天数    2104   2104    2104\n", "2025-09-01 08:42:57,855 - INFO - 第八步：保存结果文件\n", "2025-09-01 08:42:57,898 - INFO - 结果文件已保存到 ./results/ 目录\n", "2025-09-01 08:42:57,899 - INFO - 第九步：生成并保存图表\n", "2025-09-01 08:43:01,727 - INFO - 图表已保存到 ./charts/ 目录\n", "2025-09-01 08:43:01,728 - INFO - 第十步：生成年度业绩统计\n", "2025-09-01 08:43:01,760 - INFO - IC计算完成: 平均IC=1.0000, ICIR=0.0000, 样本数=1\n", "2025-09-01 08:43:01,959 - INFO - IC计算完成: 平均IC=1.0000, ICIR=0.0000, 样本数=1\n", "2025-09-01 08:43:02,050 - INFO - \n", "================================================================================\n", "2025-09-01 08:43:02,051 - INFO - 年度业绩指标统计\n", "2025-09-01 08:43:02,052 - INFO - ================================================================================\n", "2025-09-01 08:43:02,058 - INFO -      年份  组合收益(%)  基准收益(%)  超额收益(%)  年化波动率(%)  最大回撤(%)  夏普比率  胜率(%)   赔率  IC  ICIR  换手率(%)\n", "   2017    19.43    -0.33    19.76     10.44    -7.33  1.67  75.00 2.87 1.0     0   16.67\n", "   2018   -26.30   -31.98     5.68     22.27   -34.00 -1.27  75.00 0.72 0.0     0   27.27\n", "   2019    28.88    32.31    -3.43     23.16   -19.77  1.16  50.00 0.87 0.0     0   18.18\n", "   2020    35.26    21.50    13.76     26.31   -15.67  1.26  58.33 3.18 0.0     0   27.27\n", "   2021    31.11     5.50    25.62     19.31   -11.96  1.51  66.67 3.41 0.0     0   27.27\n", "   2022   -12.14   -21.05     8.91     23.10   -24.20 -0.61  50.00 1.98 0.0     0   18.18\n", "   2023   -13.60    -9.96    -3.65     14.28   -24.58 -1.09  41.67 0.87 0.0     0   45.45\n", "   2024    22.65     9.28    13.37     31.99   -22.27  0.65  66.67 1.60 0.0     0    9.09\n", "   2025    24.49    23.07     1.42     22.28   -16.87  1.01  37.50 2.40 0.0     0   28.57\n", "全样本(年化)    10.76     1.23     9.52     22.27   -37.36  0.39  58.65 1.71 1.0     0   24.22\n", "2025-09-01 08:43:02,059 - INFO - ================================================================================\n", "2025-09-01 08:43:02,060 - INFO - 年度业绩统计已保存到: ./results//0901_comprehensive_annual_performance.csv\n", "2025-09-01 08:43:02,061 - INFO - \n", "策略运行完成！\n"]}], "source": ["from main import *\n", "# 创建策略实例\n", "strategy = LargeSmallCapStrategy(config_file='config.json')\n", "\n", "# 运行单因子策略分析\n", "# results = strategy.run_single_factor_analysis(factor_name='fixed_asset_investment_factor')\n", "\n", "# 运行多因子策略\n", "results = strategy.run_comprehensive_strategy()"]}, {"cell_type": "code", "execution_count": 3, "id": "9e5a67fb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-21 14:12:58,243 - INFO - 开始运行大小盘轮动综合策略...\n", "2025-08-21 14:12:58,244 - INFO - 回测期间: 20170101 至 20250731\n", "2025-08-21 14:12:58,244 - INFO - 大盘指数: 000300.SH\n", "2025-08-21 14:12:58,245 - INFO - 小盘指数: 000852.SH\n", "2025-08-21 14:12:58,246 - INFO - 调仓频率: monthly\n", "2025-08-21 14:12:58,247 - INFO - 第一步：计算因子得分\n", "2025-08-21 14:12:58,249 - INFO - 开始计算大小盘轮动因子...\n", "2025-08-21 14:12:58,289 - INFO - 计算bond_yield_factor...\n", "2025-08-21 14:12:58,290 - INFO - 短期均线窗口大小为：20天\n", "2025-08-21 14:12:58,291 - INFO - 长期均线窗口大小为：240天\n", "2025-08-21 14:13:01,471 - INFO - 国债收益率短期均线高于长期均线时，看多大盘，低于长期均线时看空大盘\n", "2025-08-21 14:13:01,476 - INFO - 计算gold_factor...\n", "2025-08-21 14:13:01,477 - INFO - 黄金短期均线窗口大小为：20天\n", "2025-08-21 14:13:01,478 - INFO - 黄金长期均线窗口大小为：240天\n", "2025-08-21 14:13:04,798 - INFO - 黄金日收益率短期均线高于长期均线时看空大盘，低于长期均线时看多大盘\n", "2025-08-21 14:13:04,803 - INFO - 计算financing_balance_factor...\n", "2025-08-21 14:13:04,804 - INFO - 回看窗口大小为：240天\n", "2025-08-21 14:13:05,206 - INFO - 两融余额过去一年分位点高于50时看空大盘风格，低于50时看多大盘风格\n", "2025-08-21 14:13:05,211 - INFO - 计算turnover_factor...\n", "2025-08-21 14:13:05,211 - INFO - 短期窗口大小为：20天，长期窗口大小为：240天\n", "2025-08-21 14:13:05,595 - INFO - 短期换手率低于长期时看多大盘，高于长期时看空大盘\n", "2025-08-21 14:13:05,599 - INFO - 计算fixed_asset_investment_factor...\n", "2025-08-21 14:13:05,600 - INFO - 制造业固定资产投资完成额环比窗口大小为：1月\n", "2025-08-21 14:13:05,842 - INFO - 制造业固定资产投资完成额累计同比环比上升时看空大盘，下降时看多大盘\n", "2025-08-21 14:13:05,846 - INFO - 计算inflation_factor...\n", "2025-08-21 14:13:05,847 - INFO - 通胀因子环比窗口大小为：3月\n", "2025-08-21 14:13:06,140 - INFO - PPI同比平滑值环比上升时看多大盘，下降时看空大盘\n", "2025-08-21 14:13:06,144 - INFO - 计算m1_m2_factor...\n", "2025-08-21 14:13:06,144 - INFO - M1-M2剪刀差因子环比窗口大小为：3月\n", "2025-08-21 14:13:06,383 - INFO - M1同比平滑值环比下降即货币紧缩时看多大盘，上升即货币宽松时看空大盘\n", "2025-08-21 14:13:06,388 - INFO - 计算credit_spread_factor...\n", "2025-08-21 14:13:06,389 - INFO - 信用利差因子环比窗口大小为：3月\n", "2025-08-21 14:13:09,492 - INFO - 信用利差环比上升即信贷紧缩时看多大盘，下降即信贷宽松时看空大盘\n", "2025-08-21 14:13:09,499 - INFO - 因子计算完成，共计算8个因子\n", "2025-08-21 14:13:09,500 - INFO - 因子数据计算完成，共 2083 个交易日\n", "2025-08-21 14:13:09,501 - INFO - 第二步：生成交易信号\n", "2025-08-21 14:13:09,888 - INFO - 交易信号生成完成，共 104 个调仓日\n", "2025-08-21 14:13:09,889 - INFO - 第三步：运行策略回测\n"]}], "source": ["backtest_config = strategy.config.get_config('backtest')\n", "signal_config = strategy.config.get_config('signal')\n", "output_config = strategy.config.get_config('output')\n", "\n", "logging.info(\"开始运行大小盘轮动综合策略...\")\n", "logging.info(f\"回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}\")\n", "logging.info(f\"大盘指数: {backtest_config['large_cap_index_code']}\")\n", "logging.info(f\"小盘指数: {backtest_config['small_cap_index_code']}\")\n", "logging.info(f\"调仓频率: {backtest_config['rebalance_frequency']}\")\n", "\n", "# 第一步：计算所有因子得分\n", "logging.info(\"第一步：计算因子得分\")\n", "factor_data = strategy.factors.calculate_all_factors(\n", "    backtest_config['start_date'],\n", "    backtest_config['end_date']\n", ")\n", "\n", "\n", "logging.info(f\"因子数据计算完成，共 {len(factor_data)} 个交易日\")\n", "\n", "# 第二步：生成交易信号\n", "logging.info(\"第二步：生成交易信号\")\n", "signals = strategy.backtest_engine.generate_signals(\n", "    factor_data,\n", "    signal_config['signal_threshold'],\n", "    backtest_config['rebalance_frequency']\n", ")\n", "\n", "\n", "logging.info(f\"交易信号生成完成，共 {len(signals)} 个调仓日\")\n", "\n", "# 第三步：运行回测\n", "logging.info(\"第三步：运行策略回测\")\n", "backtest_results = strategy.backtest_engine.run_backtest(\n", "    signals,\n", "    backtest_config['large_cap_index_code'],\n", "    backtest_config['small_cap_index_code'],\n", "    backtest_config['start_date'],\n", "    backtest_config['end_date']\n", ")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2a048c4a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "factor_data = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__factor_data.csv')\n", "signals = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__signals.csv')\n"]}, {"cell_type": "code", "execution_count": null, "id": "7a4b0b37", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "portfolio_return", "rawType": "float64", "type": "float"}], "ref": "3e728e9d-8be5-4758-aa4f-e60d670c1de5", "rows": [["2017-01-31 00:00:00", "0.022160603308066795"], ["2017-02-28 00:00:00", "0.021245765936528382"], ["2017-03-31 00:00:00", "-0.0031862130021028667"], ["2017-04-30 00:00:00", "-0.007887295337879241"], ["2017-05-31 00:00:00", "-0.027520588744785024"], ["2017-06-30 00:00:00", "0.04862813031464985"], ["2017-07-31 00:00:00", "0.028995525232878405"], ["2017-08-31 00:00:00", "0.03729222971327428"], ["2017-09-30 00:00:00", "0.01814885003088107"], ["2017-10-31 00:00:00", "0.012633677777980745"], ["2017-11-30 00:00:00", "0.010856478196211494"], ["2017-12-31 00:00:00", "0.0025204122240551907"], ["2018-01-31 00:00:00", "0.08530701152803433"], ["2018-02-28 00:00:00", "-0.04072391634526873"], ["2018-03-31 00:00:00", "-0.043454306435859236"], ["2018-04-30 00:00:00", "-0.033022131626725404"], ["2018-05-31 00:00:00", "0.002463504336112754"], ["2018-06-30 00:00:00", "-0.07058907402461001"], ["2018-07-31 00:00:00", "0.01751209279887589"], ["2018-08-31 00:00:00", "-0.044442338891979016"], ["2018-09-30 00:00:00", "0.03549863072641757"], ["2018-10-31 00:00:00", "-0.06471842448959275"], ["2018-11-30 00:00:00", "-0.006487950869302983"], ["2018-12-31 00:00:00", "-0.04046534422454007"], ["2019-01-31 00:00:00", "0.05991354583546982"], ["2019-02-28 00:00:00", "0.10715541159780573"], ["2019-03-31 00:00:00", "0.046862080960248464"], ["2019-04-30 00:00:00", "0.01432129480398947"], ["2019-05-31 00:00:00", "-0.0695795753134113"], ["2019-06-30 00:00:00", "0.037190878246910364"], ["2019-07-31 00:00:00", "-0.004515701498998048"], ["2019-08-31 00:00:00", "-0.039709358403155814"], ["2019-09-30 00:00:00", "0.002954351191752025"], ["2019-10-31 00:00:00", "0.012915657483311582"], ["2019-11-30 00:00:00", "-0.006493812523875375"], ["2019-12-31 00:00:00", "0.06604850985070398"], ["2020-01-31 00:00:00", "0.004706468112529949"], ["2020-02-29 00:00:00", "0.01090534997019832"], ["2020-03-31 00:00:00", "-0.07142575567225584"], ["2020-04-30 00:00:00", "0.08065011472020256"], ["2020-05-31 00:00:00", "0.017535753602915483"], ["2020-06-30 00:00:00", "0.11669880447175718"], ["2020-07-31 00:00:00", "0.14134088999257344"], ["2020-08-31 00:00:00", "0.015532444813448398"], ["2020-09-30 00:00:00", "-0.05244179970129392"], ["2020-10-31 00:00:00", "0.02370855002309069"], ["2020-11-30 00:00:00", "0.03201890455602352"], ["2020-12-31 00:00:00", "0.10555607580151283"], ["2021-01-31 00:00:00", "0.04755544576841775"], ["2021-02-28 00:00:00", "-0.019716357868776346"]], "shape": {"columns": 1, "rows": 103}}, "text/plain": ["date\n", "2017-01-31    0.022161\n", "2017-02-28    0.021246\n", "2017-03-31   -0.003186\n", "2017-04-30   -0.007887\n", "2017-05-31   -0.027521\n", "                ...   \n", "2025-03-31   -0.005019\n", "2025-04-30   -0.021819\n", "2025-05-31    0.035727\n", "2025-06-30    0.017608\n", "2025-07-31    0.043177\n", "Freq: ME, Name: portfolio_return, Length: 103, dtype: float64"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["backtest_results = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__backtest_results.csv')\n", "monthly_return = backtest_results.set_index(pd.to_datetime(backtest_results['date']))['portfolio_return'].resample('ME').apply(\n", "            lambda x: (x + 1).prod() - 1\n", "        )\n", "monthly_return"]}, {"cell_type": "code", "execution_count": 7, "id": "d58d5c0d", "metadata": {}, "outputs": [], "source": ["signals['date'] = pd.to_datetime(signals['date'])\n", "signals = signals.set_index('date')\n", "backtest_results['date'] = pd.to_datetime(backtest_results['date'])\n", "backtest_results = backtest_results.set_index('date')"]}, {"cell_type": "code", "execution_count": 8, "id": "8a5bb830", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "index", "rawType": "object", "type": "string"}, {"name": "weight", "rawType": "float64", "type": "float"}, {"name": "return", "rawType": "float64", "type": "float"}], "ref": "b339a593-ee72-4a4a-990d-f48e9cba1834", "rows": [["2017-01-03 00:00:00", "large", "1.0", "0.0"], ["2017-01-26 00:00:00", "large", "0.0", "0.0035713098730705"], ["2017-02-28 00:00:00", "large", "0.0", "0.0019115130919569"], ["2017-03-31 00:00:00", "large", "1.0", "0.0056121204926271"], ["2017-04-28 00:00:00", "large", "1.0", "-0.0020213999384109"], ["2017-05-31 00:00:00", "large", "1.0", "0.0035771395784061"], ["2017-06-30 00:00:00", "large", "1.0", "-0.0005533647408209"], ["2017-07-31 00:00:00", "large", "1.0", "0.0042939995508735"], ["2017-08-31 00:00:00", "large", "1.0", "-0.0031838141011639"], ["2017-09-29 00:00:00", "large", "1.0", "0.0036527296284203"], ["2017-10-31 00:00:00", "large", "1.0", "-0.0007491542181304"], ["2017-11-30 00:00:00", "large", "1.0", "-0.0117554279147108"], ["2017-12-29 00:00:00", "large", "1.0", "0.0029753185537904"], ["2018-01-31 00:00:00", "large", "1.0", "0.0046513216870431"], ["2018-02-28 00:00:00", "large", "1.0", "-0.0087063978576295"], ["2018-03-30 00:00:00", "large", "1.0", "0.0011422298708148"], ["2018-04-27 00:00:00", "large", "1.0", "0.000368100698121"], ["2018-05-31 00:00:00", "large", "1.0", "0.0212182643884881"], ["2018-06-29 00:00:00", "large", "1.0", "0.0255464724886669"], ["2018-07-31 00:00:00", "large", "1.0", "0.0007332409048854"], ["2018-08-31 00:00:00", "large", "1.0", "-0.0049508008458849"], ["2018-09-28 00:00:00", "large", "1.0", "0.0103639680241176"], ["2018-10-31 00:00:00", "large", "0.0", "0.0140061901567409"], ["2018-11-30 00:00:00", "large", "0.0", "0.0111662400528396"], ["2018-12-28 00:00:00", "large", "0.0", "0.0067372886130929"], ["2019-01-31 00:00:00", "large", "0.0", "0.0104624834806588"], ["2019-02-28 00:00:00", "large", "0.0", "-0.0024526477207256"], ["2019-03-29 00:00:00", "large", "0.0", "0.0386080038240579"], ["2019-04-30 00:00:00", "large", "0.0", "0.0033015378503875"], ["2019-05-31 00:00:00", "large", "0.0", "-0.0031292025314958"], ["2019-06-28 00:00:00", "large", "1.0", "-0.002406868311741"], ["2019-07-31 00:00:00", "large", "1.0", "-0.0090325145409254"], ["2019-08-30 00:00:00", "large", "1.0", "0.0024799833739059"], ["2019-09-30 00:00:00", "large", "1.0", "-0.0098958291965739"], ["2019-10-31 00:00:00", "large", "1.0", "-0.001149663518601"], ["2019-11-29 00:00:00", "large", "1.0", "-0.0087078359390198"], ["2019-12-31 00:00:00", "large", "0.0", "0.003662430829775"], ["2020-01-23 00:00:00", "large", "0.0", "-0.0309854634313723"], ["2020-02-28 00:00:00", "large", "0.0", "-0.0354544675396381"], ["2020-03-31 00:00:00", "large", "0.0", "0.0032781537236166"], ["2020-04-30 00:00:00", "large", "0.0", "0.0117778182337255"], ["2020-05-29 00:00:00", "large", "1.0", "0.0026942676724905"], ["2020-06-30 00:00:00", "large", "0.0", "0.013199767263746"], ["2020-07-31 00:00:00", "large", "0.0", "0.0083535957793117"], ["2020-08-31 00:00:00", "large", "1.0", "-0.005790331214732"], ["2020-09-30 00:00:00", "large", "1.0", "-0.000959101152352"], ["2020-10-30 00:00:00", "large", "1.0", "-0.0162554585559974"], ["2020-11-30 00:00:00", "large", "1.0", "-0.0041184637299692"], ["2020-12-31 00:00:00", "large", "1.0", "0.0190816433585747"], ["2021-01-29 00:00:00", "large", "1.0", "-0.0046824310613887"]], "shape": {"columns": 3, "rows": 208}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>weight</th>\n", "      <th>return</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2017-01-03</th>\n", "      <td>large</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-01-26</th>\n", "      <td>large</td>\n", "      <td>0.0</td>\n", "      <td>0.003571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-02-28</th>\n", "      <td>large</td>\n", "      <td>0.0</td>\n", "      <td>0.001912</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-03-31</th>\n", "      <td>large</td>\n", "      <td>1.0</td>\n", "      <td>0.005612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-04-28</th>\n", "      <td>large</td>\n", "      <td>1.0</td>\n", "      <td>-0.002021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-31</th>\n", "      <td>small</td>\n", "      <td>1.0</td>\n", "      <td>-0.006648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-30</th>\n", "      <td>small</td>\n", "      <td>1.0</td>\n", "      <td>0.007917</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-30</th>\n", "      <td>small</td>\n", "      <td>1.0</td>\n", "      <td>-0.010348</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-30</th>\n", "      <td>small</td>\n", "      <td>0.0</td>\n", "      <td>0.012625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-07-31</th>\n", "      <td>small</td>\n", "      <td>0.0</td>\n", "      <td>-0.008528</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>208 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            index  weight    return\n", "date                               \n", "2017-01-03  large     1.0  0.000000\n", "2017-01-26  large     0.0  0.003571\n", "2017-02-28  large     0.0  0.001912\n", "2017-03-31  large     1.0  0.005612\n", "2017-04-28  large     1.0 -0.002021\n", "...           ...     ...       ...\n", "2025-03-31  small     1.0 -0.006648\n", "2025-04-30  small     1.0  0.007917\n", "2025-05-30  small     1.0 -0.010348\n", "2025-06-30  small     0.0  0.012625\n", "2025-07-31  small     0.0 -0.008528\n", "\n", "[208 rows x 3 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["signal_df = pd.melt(\n", "    signals[['large_cap_weight', 'small_cap_weight']].rename(\n", "        columns={\n", "            'large_cap_weight': 'large', \n", "            'small_cap_weight': 'small'\n", "            }), \n", "    value_vars=['large', 'small'], \n", "    var_name='index', \n", "    value_name='weight',\n", "    ignore_index=False\n", ")\n", "return_df = pd.melt(\n", "    backtest_results[['large_cap_return', 'small_cap_return']].rename(\n", "        columns={\n", "            'large_cap_return': 'large', \n", "            'small_cap_return': 'small'\n", "            }), \n", "    value_vars=['large', 'small'], \n", "    var_name='index', \n", "    value_name='return',\n", "    ignore_index=False\n", ")\n", "merged_df = pd.merge(signal_df, return_df, on=['index', 'date'], how='inner')\n", "merged_df"]}], "metadata": {"kernelspec": {"display_name": "common", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}