from main import *
# 创建策略实例
strategy = LargeSmallCapStrategy(config_file='config.json')

# 运行单因子策略分析
# results = strategy.run_single_factor_analysis(factor_name='fixed_asset_investment_factor')

# 运行多因子策略
results = strategy.run_comprehensive_strategy()

backtest_config = strategy.config.get_config('backtest')
signal_config = strategy.config.get_config('signal')
output_config = strategy.config.get_config('output')

logging.info("开始运行大小盘轮动综合策略...")
logging.info(f"回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}")
logging.info(f"大盘指数: {backtest_config['large_cap_index_code']}")
logging.info(f"小盘指数: {backtest_config['small_cap_index_code']}")
logging.info(f"调仓频率: {backtest_config['rebalance_frequency']}")

# 第一步：计算所有因子得分
logging.info("第一步：计算因子得分")
factor_data = strategy.factors.calculate_all_factors(
    backtest_config['start_date'],
    backtest_config['end_date']
)


logging.info(f"因子数据计算完成，共 {len(factor_data)} 个交易日")

# 第二步：生成交易信号
logging.info("第二步：生成交易信号")
signals = strategy.backtest_engine.generate_signals(
    factor_data,
    signal_config['signal_threshold'],
    backtest_config['rebalance_frequency']
)


logging.info(f"交易信号生成完成，共 {len(signals)} 个调仓日")

# 第三步：运行回测
logging.info("第三步：运行策略回测")
backtest_results = strategy.backtest_engine.run_backtest(
    signals,
    backtest_config['large_cap_index_code'],
    backtest_config['small_cap_index_code'],
    backtest_config['start_date'],
    backtest_config['end_date']
)


import pandas as pd
factor_data = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__factor_data.csv')
signals = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__signals.csv')


backtest_results = pd.read_csv('D:/财通金工/风格轮动/大小盘/results/0819__backtest_results.csv')
monthly_return = backtest_results.set_index(pd.to_datetime(backtest_results['date']))['portfolio_return'].resample('ME').apply(
            lambda x: (x + 1).prod() - 1
        )
monthly_return

signals['date'] = pd.to_datetime(signals['date'])
signals = signals.set_index('date')
backtest_results['date'] = pd.to_datetime(backtest_results['date'])
backtest_results = backtest_results.set_index('date')

signal_df = pd.melt(
    signals[['large_cap_weight', 'small_cap_weight']].rename(
        columns={
            'large_cap_weight': 'large', 
            'small_cap_weight': 'small'
            }), 
    value_vars=['large', 'small'], 
    var_name='index', 
    value_name='weight',
    ignore_index=False
)
return_df = pd.melt(
    backtest_results[['large_cap_return', 'small_cap_return']].rename(
        columns={
            'large_cap_return': 'large', 
            'small_cap_return': 'small'
            }), 
    value_vars=['large', 'small'], 
    var_name='index', 
    value_name='return',
    ignore_index=False
)
merged_df = pd.merge(signal_df, return_df, on=['index', 'date'], how='inner')
merged_df