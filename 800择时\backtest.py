"""
回测框架
支持单因子和综合因子回测，包括周度调仓逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BacktestEngine:
    """回测引擎"""

    def __init__(self, database_manager, config=None):
        """
        初始化回测引擎

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象
        """
        self.db = database_manager
        self.config = config

        # 默认参数
        self.initial_capital = 1000000  # 初始资金100万
        self.commission_rate = 0.0003   # 手续费率0.03%
        self.money_fund_return = 0.02   # 货币基金年化收益率2%

        if config:
            backtest_config = config.get_config('backtest')
            self.initial_capital = backtest_config.get('initial_capital', 1000000)
            self.commission_rate = backtest_config.get('commission_rate', 0.0003)
    
    def get_benchmark_data(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        获取基准数据
        
        策略基准为50%中证800全收益指数与50%货币基金指数
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 基准数据
        """
        # 获取指数价格数据
        index_data = self.db.get_index_price_data(index_code, start_date, end_date)
        
        if index_data.empty:
            return pd.DataFrame()
        
        data = index_data.copy()
        data = data.sort_values('date').reset_index(drop=True)
        
        # 计算指数收益率
        data['index_return'] = data['pct_change']/100
        
        # 计算货币基金日收益率（年化2%）
        data['money_fund_daily_return'] = (self.money_fund_return + 1) ** (1/252) - 1
        
        # 计算基准收益率（50%指数 + 50%货币基金）
        data['benchmark_return'] = (
            0.5 * data['index_return'] + 
            0.5 * data['money_fund_daily_return']
        )
        
        # 计算基准净值
        data['benchmark_nav'] = (1 + data['benchmark_return']).cumprod()
        
        return data[['date', 'close', 'benchmark_return', 'benchmark_nav']]

    def get_rebalance_dates(self, start_date, end_date, frequency='monthly'):
        """
        获取调仓日期

        Args:
            start_date: 开始日期
            end_date: 结束日期
            frequency: 调仓频率，'monthly'或'weekly'

        Returns:
            pandas.DatetimeIndex: 调仓日期列表
        """
        # 获取交易日历
        trading_dates = self.db.get_trading_date_range(start_date, end_date)

        if trading_dates.empty:
            return pd.DatetimeIndex([])

        rebalance_dates = [pd.to_datetime(start_date)]

        if frequency == 'monthly':
            # 每月最后一个交易日
            for date in trading_dates:
                # 检查是否是当月最后一个交易日
                month_end = date + pd.offsets.MonthEnd(0)
                month_trading_dates = trading_dates[
                    (trading_dates >= date.replace(day=1)) &
                    (trading_dates <= month_end)
                ]
                if len(month_trading_dates) > 0 and date == month_trading_dates.max():
                    rebalance_dates.append(date)

        elif frequency == 'weekly':
            # 每周最后一个交易日
            for date in trading_dates:
                # 检查是否是当周最后一个交易日
                week_end = date + pd.Timedelta(days=(6 - date.weekday()))
                week_trading_dates = trading_dates[
                    (trading_dates >= date) &
                    (trading_dates <= week_end)
                ]
                if len(week_trading_dates) > 0 and date == week_trading_dates.max():
                    rebalance_dates.append(date)

        return pd.DatetimeIndex(rebalance_dates)

    def generate_monthly_signals(self, factor_data, signal_threshold=0):
        """
        生成月度交易信号

        Args:
            factor_data: 因子数据DataFrame
            signal_threshold: 信号阈值

        Returns:
            pandas.DataFrame: 包含交易信号的数据
        """
        if factor_data.empty:
            return pd.DataFrame()

        # 获取调仓日期
        start_date = factor_data['date'].min().strftime('%Y%m%d')
        end_date = factor_data['date'].max().strftime('%Y%m%d')
        rebalance_dates = self.get_rebalance_dates(start_date, end_date, 'monthly')

        # 筛选调仓日的因子数据
        factor_data['date'] = pd.to_datetime(factor_data['date'])
        signal_data = factor_data[factor_data['date'].isin(rebalance_dates)].copy()

        if signal_data.empty:
            return pd.DataFrame()

        # 生成信号（基于综合得分）
        if 'comprehensive_score' in signal_data.columns:
            score_col = 'comprehensive_score'
        elif 'total_score' in signal_data.columns:
            score_col = 'total_score'
        else:
            # 如果没有综合得分，使用第一个得分列
            score_cols = [col for col in signal_data.columns if col.endswith('_score')]
            if score_cols:
                score_col = score_cols[0]
            else:
                return pd.DataFrame()

        # 生成权重信号
        signal_data['equity_weight'] = np.where(
            signal_data[score_col] >= signal_threshold, 0.8, 0.2  # 看多时80%股票，看空时20%股票
        )
        signal_data['money_fund_weight'] = 1 - signal_data['equity_weight']

        return signal_data[['date', score_col, 'equity_weight', 'money_fund_weight']].reset_index(drop=True)

    def run_backtest(self, signals_df, index_code='000906.SH', mode='comprehensive'):
        """
        运行回测
        
        Args:
            signals_df: 包含交易信号的DataFrame
            index_code: 指数代码
            mode: 回测模式，'comprehensive'为综合因子，'single'为单因子
            
        Returns:
            pandas.DataFrame: 回测结果
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        # 获取价格数据
        start_date = signals_df['date'].min().strftime('%Y%m%d')
        end_date = signals_df['date'].max().strftime('%Y%m%d')
        price_data = self.db.get_index_price_data(index_code, start_date, end_date)
        
        if price_data.empty:
            return pd.DataFrame()
        
        # 合并信号和价格数据
        signals_df['date'] = pd.to_datetime(signals_df['date'])
        price_data['date'] = pd.to_datetime(price_data['date'])

        # 如果signals_df中没有code列，添加code列
        if 'code' not in signals_df.columns:
            signals_df['code'] = index_code

        # 扩展信号到每日
        signals_df['date'] = pd.to_datetime(signals_df['date'])
        daily_signals = pd.merge_asof(
            price_data[['date']],
            signals_df,
            on='date',
            direction='backward'
        )
        
        # 合并价格和信号数据
        backtest_data = pd.merge(price_data, daily_signals, on='date', how='left')
        backtest_data = backtest_data.fillna(method='ffill')

        # 初始化回测变量
        backtest_data['portfolio_value'] = self.initial_capital
        backtest_data['equity_value'] = 0
        backtest_data['money_fund_value'] = 0
        backtest_data['daily_return'] = 0
        backtest_data['cumulative_return'] = 0
        backtest_data['transaction_cost'] = 0

        # 使用t+1日信号数据
        backtest_data['equity_weight'] = backtest_data['equity_weight'].shift(1).fillna(0)
        backtest_data['money_fund_weight'] = backtest_data['money_fund_weight'].shift(1).fillna(0)
        
        # 计算日收益率
        backtest_data['index_return'] = backtest_data['pct_change']/100
        backtest_data['money_fund_daily_return'] = (self.money_fund_return + 1) ** (1/252) - 1
        
        # 逐日计算组合价值
        for i in range(1, len(backtest_data)):
            # 计算前一日的权重
            prev_equity_weight = backtest_data.loc[i-1, 'equity_weight']
            prev_money_weight = backtest_data.loc[i-1, 'money_fund_weight']
            
            # 当前权重
            curr_equity_weight = backtest_data.loc[i, 'equity_weight']
            curr_money_weight = backtest_data.loc[i, 'money_fund_weight']
            
            # 前一日组合价值
            prev_portfolio_value = backtest_data.loc[i-1, 'portfolio_value']
            
            # 计算当日收益（基于前一日权重）
            if not pd.isna(backtest_data.loc[i, 'index_return']):
                equity_return = backtest_data.loc[i, 'index_return']
            else:
                equity_return = 0
            
            money_return = backtest_data.loc[i, 'money_fund_daily_return']
            
            # 组合日收益率
            portfolio_return = (
                curr_equity_weight * equity_return + 
                curr_money_weight * money_return
            )
            
            # 计算交易成本（权重变化时产生）
            weight_change = abs(curr_equity_weight - prev_equity_weight)
            transaction_cost = prev_portfolio_value * weight_change * self.commission_rate
            
            # 更新组合价值
            portfolio_value = prev_portfolio_value * (1 + portfolio_return) - transaction_cost
            
            backtest_data.loc[i, 'portfolio_value'] = portfolio_value
            backtest_data.loc[i, 'daily_return'] = portfolio_return
            backtest_data.loc[i, 'transaction_cost'] = transaction_cost
            backtest_data.loc[i, 'cumulative_return'] = (
                portfolio_value / self.initial_capital - 1
            )
            
            # 更新各部分价值
            backtest_data.loc[i, 'equity_value'] = portfolio_value * curr_equity_weight
            backtest_data.loc[i, 'money_fund_value'] = portfolio_value * curr_money_weight
        
        # 计算净值
        backtest_data['nav'] = backtest_data['portfolio_value'] / self.initial_capital
        
        return backtest_data
    
    def run_weekly_backtest(self, signals_df, index_code='000906.SH'):
        """
        运行周度调仓回测
        
        使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖
        
        Args:
            signals_df: 包含交易信号的DataFrame
            index_code: 指数代码
            
        Returns:
            pandas.DataFrame: 周度回测结果
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        # 获取周度信号
        from .factor_scoring import FactorScoring
        factor_scoring = FactorScoring(self.db)
        weekly_signals = factor_scoring.get_weekly_signals(signals_df)
        
        # 运行回测
        return self.run_backtest(weekly_signals, index_code)
    
    def calculate_n_day_returns(self, backtest_results, 
                                    forward_days=[1, 3, 5, 10, 20, 30, 60, 120]):
        """
        计算信号n日后的平均收益率
        
        计算每个信号产生后n日的平均收益率，用以比较信号本身的效果
        
        Args:
            backtest_results: 回测结果
            forward_days: 前瞻天数列表
            
        Returns:
            pandas.DataFrame: 信号n日后收益率分析结果
        """
        if backtest_results.empty:
            return pd.DataFrame()
            
        # 计算前瞻收益率
        for days in forward_days:
            backtest_results[f'forward_return_{days}d'] = (
                backtest_results['close'].shift(-days) / backtest_results['close'] - 1
            )
        
        # 分析信号有效性
        results = []
        
        # 分析信号大于0的信号
        positive_signals = backtest_results[backtest_results['signal'] > 0]
        
        if not positive_signals.empty:
            for days in forward_days:
                avg_return = positive_signals[f'forward_return_{days}d'].mean()
                win_rate = (positive_signals[f'forward_return_{days}d'] > 0).mean()
                
                results.append({
                    'signal_type': 'positive_score',
                    'forward_days': days,
                    'avg_return': avg_return,
                    'win_rate': win_rate,
                    'signal_count': len(positive_signals)
                })
        
        # 分析信号小于0的信号
        negative_signals = backtest_results[backtest_results['signal'] < 0]
        
        if not negative_signals.empty:
            for days in forward_days:
                avg_return = negative_signals[f'forward_return_{days}d'].mean()
                win_rate = (negative_signals[f'forward_return_{days}d'] > 0).mean()
                
                results.append({
                    'signal_type': 'negative_score',
                    'forward_days': days,
                    'avg_return': avg_return,
                    'win_rate': win_rate,
                    'signal_count': len(negative_signals)
                })
        
        return pd.DataFrame(results)
    
    def calculate_performance_metrics(self, backtest_results):
        """
        计算业绩指标（参考价值成长项目算法）

        Args:
            backtest_results: 回测结果DataFrame

        Returns:
            dict: 业绩指标
        """
        if backtest_results.empty or 'nav' not in backtest_results.columns:
            return {}

        # 基础数据
        nav_series = backtest_results['nav']
        daily_returns = backtest_results['daily_return'].dropna()

        if len(nav_series) < 2:
            return {}

        # 总收益率
        total_return = nav_series.iloc[-1] - 1

        # 年化收益率
        trading_days = len(nav_series)
        years = trading_days / 252
        annualized_return = (nav_series.iloc[-1] ** (1/years)) - 1 if years > 0 else 0

        # 年化波动率
        annualized_volatility = daily_returns.std() * np.sqrt(252) if len(daily_returns) > 1 else 0

        # 夏普比率（假设无风险利率为2%）
        risk_free_rate = 0.02
        sharpe_ratio = (annualized_return - risk_free_rate) / annualized_volatility if annualized_volatility > 0 else 0

        # 最大回撤
        cumulative_max = nav_series.cummax()
        drawdown = (nav_series - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min()

        # 月度胜率和盈亏比（使用月度收益率）
        backtest_results_copy = backtest_results.copy()
        backtest_results_copy['date'] = pd.to_datetime(backtest_results_copy['date'])
        monthly_return = backtest_results_copy.set_index('date')['daily_return'].resample('ME').apply(
            lambda x: (x + 1).prod() - 1
        )
        win_rate = (monthly_return > 0).mean() if len(monthly_return) > 0 else 0

        # 盈亏比（基于月度收益率）
        positive_returns = monthly_return[monthly_return > 0]
        negative_returns = monthly_return[monthly_return < 0]
        profit_loss_ratio = (positive_returns.mean() / abs(negative_returns.mean())) if len(negative_returns) > 0 and negative_returns.mean() != 0 else 0

        # 交易成本
        total_transaction_cost = backtest_results['transaction_cost'].sum()
        transaction_cost_ratio = total_transaction_cost / self.initial_capital
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'total_transaction_cost': total_transaction_cost,
            'transaction_cost_ratio': transaction_cost_ratio,
            'trading_days': trading_days
        }

    def get_backtest_summary(self, backtest_results):
        """
        获取回测结果汇总（兼容方法）

        Args:
            backtest_results: 回测结果DataFrame

        Returns:
            dict: 回测汇总统计
        """
        return self.calculate_performance_metrics(backtest_results)

    def validate_signal_effectiveness(self, backtest_results, benchmark_data=None, factor_name=None):
        """
        计算组合业绩指标（简化版，专注组合表现）

        Args:
            backtest_results: 回测结果DataFrame
            benchmark_data: 基准数据DataFrame
            factor_name: 因子名称（用于单因子分析）

        Returns:
            dict: 组合业绩指标
        """
        if backtest_results.empty:
            return {}

        # 直接使用calculate_performance_metrics方法
        performance_metrics = self.calculate_performance_metrics(backtest_results)

        # 添加因子名称和指数代码
        if 'code' in backtest_results.columns:
            performance_metrics['index_code'] = backtest_results['code'].iloc[0]
        performance_metrics['factor_name'] = factor_name

        # 计算信息比率（如果有基准数据）
        if benchmark_data is not None and not benchmark_data.empty:
            merged_data = backtest_results.merge(benchmark_data[['date', 'benchmark_return']],
                                               on='date', how='inner')
            if not merged_data.empty:
                excess_returns = merged_data['daily_return'] - merged_data['benchmark_return']
                excess_return = (1 + excess_returns).cumprod().iloc[-1] ** (252 / len(excess_returns)) - 1
                tracking_error = excess_returns.std() * np.sqrt(252)
                information_ratio = excess_return / tracking_error if tracking_error != 0 else 0

                performance_metrics.update({
                    'information_ratio': information_ratio,
                    'excess_return': excess_return,
                    'tracking_error': tracking_error
                })

        return performance_metrics

    def run_single_factor_backtest(self, signals, factor_name, index_code='000906.SH',
                                  signal_threshold=0, position_base=0.5, position_multiplier=0.5):
        """
        运行单因子回测

        Args:
            factor_scores: 包含因子得分的DataFrame
            factor_name: 因子名称
            index_code: 指数代码
            signal_threshold: 信号阈值
            position_base: 基础仓位
            position_multiplier: 仓位调整倍数

        Returns:
            dict: 包含回测结果和有效性指标的字典
        """
        if signals.empty:
            return {'backtest_results': pd.DataFrame(), 'effectiveness': {}}

        # 运行回测
        backtest_results = self.run_backtest(signals, index_code, mode='single')

        # 获取基准数据
        start_date = signals['date'].min().strftime('%Y-%m-%d')
        end_date = signals['date'].max().strftime('%Y-%m-%d')
        benchmark_data = self.get_benchmark_data(index_code, start_date, end_date)

        # 检验信号有效性
        effectiveness = self.validate_signal_effectiveness(backtest_results, benchmark_data, factor_name)

        return {
            'backtest_results': backtest_results,
            'effectiveness': effectiveness,
            'signals': signals
        }
