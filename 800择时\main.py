"""
中证800择时策略主程序
整合所有模块，提供完整的策略运行框架
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append('D:\财通金工\数据库')
from database import DatabaseManager
from factor_scoring import FactorScoring
from backtest import BacktestEngine
from visualization import Visualization
from config import StrategyConfig


class CSI800TimingStrategy:
    """中证800择时策略主类"""
    
    def __init__(self, config_file=None):
        """
        初始化策略
        
        Args:
            config_file: 配置文件路径
        """
        print("=" * 60)
        print("中证800择时策略系统")
        print("=" * 60)
        
        # 加载配置
        self.config = StrategyConfig(config_file)
        
        # 验证配置
        is_valid, errors = self.config.validate_config()
        if not is_valid:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            raise ValueError("配置文件存在错误")
        
        # 初始化各个模块
        print("正在初始化数据库连接...")
        self.db = DatabaseManager(self.config.get_config('database'))
        
        print("正在初始化因子计算模块...")
        self.factor_scoring = FactorScoring(self.db, self.config)
        
        print("正在初始化回测引擎...")
        self.backtest_engine = BacktestEngine(self.db)
        
        print("正在初始化可视化模块...")
        self.visualization = Visualization()
        
        print("初始化完成！\n")

    def run_single_factor_backtest(self, factor_name, factor_type='auto'):
        """
        运行单因子回测分析

        Args:
            factor_name: 因子名称（如'production_score', 'macro_score'等）
            factor_type: 因子类型，'auto'为自动识别

        Returns:
            dict: 单因子回测结果
        """
        backtest_config = self.config.get_config('backtest')
        signal_config = self.config.get_config('signal')

        print(f"开始运行{factor_name}单因子回测...")

        # 计算各个因子
        factors_function_map = {
            'macro_score': self.factor_scoring.macro_factors.calculate_all_macro_factors,
            'meso_score': self.factor_scoring.meso_factors.calculate_all_meso_factors,
            'micro_score': self.factor_scoring.micro_factors.calculate_all_micro_factors,
            'production_score': self.factor_scoring.macro_factors.calculate_production_factor,
            'consumption_score': self.factor_scoring.macro_factors.calculate_consumption_factor,
            'investment_score': self.factor_scoring.macro_factors.calculate_investment_factor,
            'monetary_score': self.factor_scoring.macro_factors.calculate_monetary_factor,
            'exchange_rate_score': self.factor_scoring.macro_factors.calculate_exchange_rate_factor,
            'bond_score': self.factor_scoring.macro_factors.calculate_bond_factor,
            'credit_score': self.factor_scoring.macro_factors.calculate_credit_factor,
            'margin_trading_score': self.factor_scoring.meso_factors.calculate_margin_trading_factor,
            'turnover_trend_score': self.factor_scoring.meso_factors.calculate_turnover_trend_factor,
            'market_money_flow_score': self.factor_scoring.meso_factors.calculate_market_money_flow_factor,
            'main_money_flow_score': self.factor_scoring.meso_factors.calculate_main_money_flow_factor,
            'vix_score': self.factor_scoring.meso_factors.calculate_vix_factor,
            'value_score': self.factor_scoring.micro_factors.calculate_value_factor,
            'momentum_score': self.factor_scoring.micro_factors.calculate_momentum_factor,
            'roe_score': self.factor_scoring.micro_factors.calculate_roe_factor,
            'liquidity_score': self.factor_scoring.micro_factors.calculate_liquidity_factor,
            'bias_score': self.factor_scoring.micro_factors.calculate_bias_factor,
            'aroon_score': self.factor_scoring.micro_factors.calculate_aroon_factor,
            'adx_score': self.factor_scoring.micro_factors.calculate_adx_factor,
            'uo_score': self.factor_scoring.micro_factors.calculate_uo_factor,
            'atr_score': self.factor_scoring.micro_factors.calculate_atr_factor,
            'udvd_score': self.factor_scoring.micro_factors.calculate_udvd_factor,
            'new_high_low_score': self.factor_scoring.micro_factors.calculate_new_high_low_factor
        }

        print(f"计算{factor_name}...")
        try:
            factor_data = factors_function_map[factor_name](
                backtest_config['index_code'],
                backtest_config['start_date'], 
                backtest_config['end_date'])
        except Exception as e:
            print(f"计算{factor_name}时出错: {e}")
            return {}
        
        if 'code' not in factor_data.columns:
            factor_data['code'] = backtest_config['index_code']

        signal_data = self.factor_scoring.generate_single_factor_signals(
            factor_data, 
            factor_name, 
            signal_config.get('signal_threshold', 0),
            signal_config.get('position_base', 0.5),
            signal_config.get('position_multiplier', 0.5)
        )

        # 运行单因子回测
        backtest_result = self.backtest_engine.run_single_factor_backtest(
            signal_data,
            factor_name,
            backtest_config['index_code'],
            signal_config.get('signal_threshold', 0),
            signal_config.get('position_base', 0.5),
            signal_config.get('position_multiplier', 0.5)
        )

        print(f"{factor_name}单因子回测完成！")

        # 打印关键指标
        if 'effectiveness' in backtest_result:
            eff = backtest_result['effectiveness']
            print(f"年化收益率: {eff.get('annualized_return', 0):.2%}")
            print(f"年化波动率: {eff.get('annualized_volatility', 0):.2%}")
            print(f"夏普比率: {eff.get('sharpe_ratio', 0):.3f}")
            print(f"信息比率: {eff.get('information_ratio', 0):.3f}")
            print(f"最大回撤: {eff.get('max_drawdown', 0):.2%}")
            print(f"胜率: {eff.get('win_rate', 0):.2%}")

        return backtest_result

    def run_factor_category_analysis(self, category='macro'):
        """
        运行因子大类分析

        Args:
            category: 因子大类（'macro', 'meso', 'micro', 'technical'）

        Returns:
            dict: 因子大类分析结果
        """
        backtest_config = self.config.get_config('backtest')

        print(f"开始运行{category}因子大类分析...")

        # 获取所有因子数据
        all_factors = self.factor_scoring.calculate_all_factors(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        if all_factors.empty:
            print("错误：无法获取因子数据")
            return {}

        # 计算因子大类得分
        category_scores = self.factor_scoring.calculate_factor_category_score(all_factors, category)

        if category_scores.empty:
            print(f"错误：无法计算{category}因子大类得分")
            return {}

        # 运行大类因子回测
        category_score_col = f'{category}_category_score'
        backtest_result = self.run_single_factor_backtest(category_score_col)

        print(f"{category}因子大类分析完成！")

        return {
            'category': category,
            'category_scores': category_scores,
            'backtest_result': backtest_result
        }

    def compare_factor_effectiveness(self, factor_list=None):
        """
        比较多个因子的有效性

        Args:
            factor_list: 因子列表，None时比较所有主要因子

        Returns:
            pandas.DataFrame: 因子有效性比较结果
        """
        if factor_list is None:
            factor_list = [
                'macro_score', 'meso_score', 'micro_score',
                'production_score', 'consumption_score', 'investment_score',
                'monetary_score', 'exchange_rate_score', 'bond_score',
                'margin_trading_score', 'turnover_trend_score', 'vix_score',
                'value_score', 'momentum_score', 'roe_score'
            ]

        print("开始比较因子有效性...")

        results = []
        for factor_name in factor_list:
            print(f"分析因子: {factor_name}")
            try:
                result = self.run_single_factor_backtest(factor_name)
                if 'effectiveness' in result:
                    eff = result['effectiveness']
                    eff['factor_name'] = factor_name
                    results.append(eff)
            except Exception as e:
                print(f"分析{factor_name}时出错: {e}")
                continue

        if results:
            comparison_df = pd.DataFrame(results)
            # 按夏普比率排序
            comparison_df = comparison_df.sort_values('sharpe_ratio', ascending=False)
            print("因子有效性比较完成！")
            return comparison_df
        else:
            print("无法获取因子比较结果")
            return pd.DataFrame()

    def run_strategy(self, save_results=True):
        """
        运行完整策略
        
        Args:
            save_results: 是否保存结果
            
        Returns:
            dict: 策略运行结果
        """
        backtest_config = self.config.get_config('backtest')
        output_config = self.config.get_config('output')
        
        print("开始运行中证800择时策略...")
        print(f"回测期间: {backtest_config['start_date']} 至 {backtest_config['end_date']}")
        print(f"目标指数: {backtest_config['index_code']}")
        print()
        
        # 第一步：计算所有因子得分
        print("第一步：计算因子得分")
        factor_scores = self.factor_scoring.calculate_all_factors(
            index_code=backtest_config['index_code'],
            start_date=backtest_config['start_date'],
            end_date=backtest_config['end_date']
        )
        
        if factor_scores.empty:
            print("错误：无法获取因子数据")
            return {}
        
        print(f"因子数据计算完成，共 {len(factor_scores)} 个交易日")
        
        # 第二步：计算综合得分
        print("\n第二步：计算综合得分")
        factor_weights = self.config.get_config('factor_weights')
        comprehensive_scores = self.factor_scoring.calculate_comprehensive_score(
            factor_scores, factor_weights
        )
        
        # 第三步：生成月度交易信号
        print("第三步：生成月度交易信号")
        signal_config = self.config.get_config('signal')
        signals = self.factor_scoring.generate_monthly_signals(
            comprehensive_scores, signal_config['signal_threshold']
        )
        
        # 第四步：运行回测
        print("第四步：运行策略回测")
        backtest_results = self.backtest_engine.run_backtest(
            signals, backtest_config['index_code']
        )
        
        if backtest_results.empty:
            print("错误：回测失败")
            return {}
        
        # 第四步：获取基准数据
        print("第四步：计算基准表现")
        benchmark_metrics = self.backtest_engine.get_benchmark_performance(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )
        excess_metrics = self.backtest_engine.get_excess_performance(
            backtest_results,
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        # 第五步：计算业绩指标
        print("第五步：计算业绩指标")
        strategy_metrics = self.backtest_engine.calculate_performance_metrics(backtest_results)

        # 第六步：生成报告
        print("第六步：生成分析报告")
        performance_report = self.visualization.create_performance_report(
            strategy_metrics, benchmark_metrics, excess_metrics
        )
        
        # 打印业绩报告
        print("\n" + "=" * 50)
        print("策略业绩报告")
        print("=" * 50)
        print(performance_report.to_string(index=False))
        print()
        
        # 第八步：保存结果和图表
        if save_results and output_config['save_results']:
            print("第八步：保存结果文件")
            self._save_results(
                factor_scores, signals, backtest_results, 
                performance_report, output_config
            )
        
        # 初始化annual_performance
        annual_performance = pd.DataFrame()

        if output_config['save_charts']:
            print("第九步：生成并保存图表")
            # 获取基准数据用于图表
            benchmark_data = self.backtest_engine.get_benchmark_data(
                backtest_config['index_code'],
                backtest_config['start_date'],
                backtest_config['end_date']
            )
            self.visualization.save_all_charts(
                backtest_results, benchmark_data, factor_scores,
                output_config['chart_dir'], output_config['file_prefix']
            )

            # 第十步：生成年度业绩统计
            print("第十步：生成年度业绩统计")
            annual_performance = self.visualization.calculate_annual_performance(
                backtest_results, benchmark_data,
                factor_data=factor_scores, signals=signals,
                backtest_engine=self.backtest_engine,
                output_dir=output_config['result_dir'],
                file_prefix=output_config['file_prefix']
            )

        print("\n策略运行完成！")

        return {
            'factor_scores': factor_scores,
            'signals': signals,
            'backtest_results': backtest_results,
            'benchmark_metrics': benchmark_metrics,
            'strategy_metrics': strategy_metrics,
            'excess_metrics': excess_metrics,
            'performance_report': performance_report,
            'annual_performance': annual_performance
        }
    
    def run_factor_analysis(self, factor_type='all'):
        """
        运行单因子分析

        Args:
            factor_type: 因子类型 ('macro', 'meso', 'micro', 'all')

        Returns:
            dict: 因子分析结果
        """
        backtest_config = self.config.get_config('backtest')

        print(f"开始运行{factor_type}因子分析...")

        if factor_type == 'macro':
            factor_scores = self.factor_scoring.macro_factors.calculate_all_macro_factors(
                backtest_config['start_date'], backtest_config['end_date']
            )
        elif factor_type == 'meso':
            factor_scores = self.factor_scoring.meso_factors.calculate_all_meso_factors(
                backtest_config['index_code'], backtest_config['start_date'], backtest_config['end_date']
            )
        elif factor_type == 'micro':
            factor_scores = self.factor_scoring.micro_factors.calculate_all_micro_factors(
                backtest_config['index_code'], backtest_config['start_date'], backtest_config['end_date']
            )
        else:
            return self.run_strategy()

        if factor_scores.empty:
            print(f"错误：无法获取{factor_type}因子数据")
            return {}

        # 生成单因子信号和回测
        score_col = f'{factor_type}_score'
        if score_col in factor_scores.columns:
            signals = factor_scores.copy()
            signals['comprehensive_score'] = signals[score_col]
            signals = self.factor_scoring.generate_trading_signals(signals)

            backtest_results = self.backtest_engine.run_backtest(
                signals, backtest_config['index_code']
            )

            summary = self.backtest_engine.get_backtest_summary(backtest_results)

            print(f"\n{factor_type}因子分析完成")
            print(f"总收益率: {summary.get('total_return', 0):.2%}")
            print(f"年化收益率: {summary.get('annualized_return', 0):.2%}")
            print(f"夏普比率: {summary.get('sharpe_ratio', 0):.3f}")
            print(f"最大回撤: {summary.get('max_drawdown', 0):.2%}")

            return {
                'factor_scores': factor_scores,
                'signals': signals,
                'backtest_results': backtest_results,
                'summary': summary
            }

        return {}
    
    def _save_results(self, factor_scores, signals, backtest_results,
                     performance_report, output_config):
        """
        保存结果文件

        Args:
            factor_scores: 因子得分
            signals: 交易信号
            backtest_results: 回测结果
            performance_report: 业绩报告
            output_config: 输出配置
        """
        result_dir = output_config['result_dir']
        prefix = output_config['file_prefix']

        os.makedirs(result_dir, exist_ok=True)

        # 保存因子得分（因子值）
        factor_scores.to_csv(f'{result_dir}/{prefix}_factor_scores.csv', index=False, encoding='utf-8-sig')

        # 保存交易信号
        signals.to_csv(f'{result_dir}/{prefix}_signals.csv', index=False, encoding='utf-8-sig')

        # 保存回测结果
        backtest_results.to_csv(f'{result_dir}/{prefix}_backtest_results.csv', index=False, encoding='utf-8-sig')

        # 保存业绩报告
        performance_report.to_csv(f'{result_dir}/{prefix}_performance_report.csv', index=False, encoding='utf-8-sig')

        # 生成简单业绩概述txt
        self._generate_performance_summary_txt(backtest_results, result_dir, prefix)

        print(f"结果文件已保存到 {result_dir} 目录")

    def _generate_performance_summary_txt(self, backtest_results, result_dir, prefix):
        """
        生成简单业绩概述txt文件

        Args:
            backtest_results: 回测结果
            result_dir: 输出目录
            prefix: 文件前缀
        """
        try:
            # 计算基本业绩指标
            performance_metrics = self.backtest_engine.calculate_performance_metrics(backtest_results)

            # 生成概述文本
            summary_text = f"""
800择时策略业绩概述
{'='*50}

策略基本信息：
- 策略名称：中证800择时策略
- 回测期间：{backtest_results['date'].min()} 至 {backtest_results['date'].max()}
- 调仓频率：月度调仓
- 基准指数：中证800指数

核心业绩指标：
- 总收益率：{performance_metrics.get('total_return', 0):.2%}
- 年化收益率：{performance_metrics.get('annualized_return', 0):.2%}
- 年化波动率：{performance_metrics.get('annualized_volatility', 0):.2%}
- 夏普比率：{performance_metrics.get('sharpe_ratio', 0):.3f}
- 最大回撤：{abs(performance_metrics.get('max_drawdown', 0)):.2%}

风险收益特征：
- 月度胜率：{performance_metrics.get('win_rate', 0):.2%}
- 月度赔率：{performance_metrics.get('profit_loss_ratio', 0):.3f}
- 交易成本：{performance_metrics.get('transaction_cost_ratio', 0):.4%}

策略说明：
本策略基于宏观、中观、微观三个层面的因子构建择时模型，
通过月度调仓实现对中证800指数的择时配置。
策略在看多时配置80%股票仓位，看空时配置20%股票仓位。

生成时间：{pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            # 保存到文件
            with open(f'{result_dir}/{prefix}_performance_summary.txt', 'w', encoding='utf-8') as f:
                f.write(summary_text)

            print(f"业绩概述已保存至: {result_dir}/{prefix}_performance_summary.txt")

        except Exception as e:
            print(f"生成业绩概述时出错: {e}")

    def visualize_single_factor(self, factor_name, save_chart=True):
        """
        可视化单因子分析结果

        Args:
            factor_name: 因子名称
            save_chart: 是否保存图表

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        backtest_config = self.config.get_config('backtest')
        output_config = self.config.get_config('output')

        print(f"开始可视化{factor_name}因子...")

        # 获取因子数据
        all_factors = self.factor_scoring.calculate_all_factors(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        if all_factors.empty or factor_name not in all_factors.columns:
            print(f"错误：无法获取因子{factor_name}的数据")
            return None

        # 获取基准数据
        benchmark_data = self.backtest_engine.get_benchmark_data(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        # 运行单因子回测
        backtest_result = self.run_single_factor_backtest(factor_name)

        if 'backtest_results' not in backtest_result:
            print(f"错误：无法获取{factor_name}的回测结果")
            return None

        # 创建可视化
        fig = self.visualization.plot_single_factor_analysis(
            all_factors, benchmark_data, backtest_result['backtest_results'], factor_name
        )

        # 保存图表
        if save_chart and output_config.get('save_charts', True):
            chart_dir = output_config.get('chart_dir', './charts/')
            prefix = output_config.get('file_prefix', 'factor_analysis')

            import os
            os.makedirs(chart_dir, exist_ok=True)

            fig.savefig(f'{chart_dir}/{prefix}_{factor_name}_analysis.png',
                       dpi=300, bbox_inches='tight')
            print(f"图表已保存到 {chart_dir}/{prefix}_{factor_name}_analysis.png")

        return fig

    def visualize_factor_comparison(self, factor_list=None, save_chart=True):
        """
        可视化因子有效性比较

        Args:
            factor_list: 因子列表
            save_chart: 是否保存图表

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        print("开始可视化因子有效性比较...")

        # 获取因子比较结果
        comparison_df = self.compare_factor_effectiveness(factor_list)

        if comparison_df.empty:
            print("错误：无法获取因子比较数据")
            return None

        # 创建可视化
        fig = self.visualization.plot_factor_effectiveness_comparison(comparison_df)

        # 保存图表
        if save_chart:
            output_config = self.config.get_config('output')
            chart_dir = output_config.get('chart_dir', './charts/')
            prefix = output_config.get('file_prefix', 'factor_analysis')

            import os
            os.makedirs(chart_dir, exist_ok=True)

            fig.savefig(f'{chart_dir}/{prefix}_factor_comparison.png',
                       dpi=300, bbox_inches='tight')
            print(f"图表已保存到 {chart_dir}/{prefix}_factor_comparison.png")

        return fig

    def visualize_factor_with_benchmark(self, factor_name, save_chart=True):
        """
        可视化因子与基准对比

        Args:
            factor_name: 因子名称
            save_chart: 是否保存图表

        Returns:
            matplotlib.figure.Figure: 图表对象
        """
        backtest_config = self.config.get_config('backtest')
        output_config = self.config.get_config('output')

        print(f"开始可视化{factor_name}因子与基准对比...")

        # 获取因子数据
        all_factors = self.factor_scoring.calculate_all_factors(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        if all_factors.empty or factor_name not in all_factors.columns:
            print(f"错误：无法获取因子{factor_name}的数据")
            return None

        # 获取基准数据
        benchmark_data = self.backtest_engine.get_benchmark_data(
            backtest_config['index_code'],
            backtest_config['start_date'],
            backtest_config['end_date']
        )

        # 创建可视化
        fig = self.visualization.plot_factor_with_benchmark(
            all_factors, benchmark_data, factor_name
        )

        # 保存图表
        if save_chart and output_config.get('save_charts', True):
            chart_dir = output_config.get('chart_dir', './charts/')
            prefix = output_config.get('file_prefix', 'factor_analysis')

            import os
            os.makedirs(chart_dir, exist_ok=True)

            fig.savefig(f'{chart_dir}/{prefix}_{factor_name}_benchmark.png',
                       dpi=300, bbox_inches='tight')
            print(f"图表已保存到 {chart_dir}/{prefix}_{factor_name}_benchmark.png")

        return fig

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'db'):
            self.db.close()


def main():
    """主函数"""
    try:
        # 创建策略实例
        strategy = CSI800TimingStrategy()
        
        # 运行策略
        results = strategy.run_strategy()
        
        # 可选：运行单因子分析
        # valuation_results = strategy.run_factor_analysis('valuation')
        # fundamental_results = strategy.run_factor_analysis('fundamental')
        # capital_results = strategy.run_factor_analysis('capital')
        # technical_results = strategy.run_factor_analysis('technical')
        
    except Exception as e:
        print(f"策略运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if 'strategy' in locals():
            strategy.close()


if __name__ == "__main__":
    main()
