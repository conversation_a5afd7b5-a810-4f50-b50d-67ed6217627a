{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3176f151", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-29 09:38:21,445 - INFO - 配置文件加载成功: config.json\n", "2025-08-29 09:38:21,475 - INFO - ================================================================================\n", "2025-08-29 09:38:21,478 - INFO - 股债轮动因子计算器初始化\n", "2025-08-29 09:38:21,478 - INFO - ================================================================================\n", "2025-08-29 09:38:21,479 - INFO - 货币政策因子参数:\n", "2025-08-29 09:38:21,479 - INFO -   DR007平滑期: 20天\n", "2025-08-29 09:38:21,480 - INFO -   DR007比较期: 60天\n", "2025-08-29 09:38:21,481 - INFO -   SHIBOR平滑期: 20天\n", "2025-08-29 09:38:21,482 - INFO -   SHIBOR比较期: 60天\n", "2025-08-29 09:38:21,483 - INFO - 信用周期因子参数:\n", "2025-08-29 09:38:21,483 - INFO -   社融平滑期: 12月\n", "2025-08-29 09:38:21,484 - INFO -   社融比较期: 3月\n", "2025-08-29 09:38:21,485 - INFO -   信贷冲量平滑期: 12月\n", "2025-08-29 09:38:21,485 - INFO -   信贷冲量比较期: 3月\n", "2025-08-29 09:38:21,486 - INFO - 因子权重参数:\n", "2025-08-29 09:38:21,487 - INFO -   DR007权重: 0.5\n", "2025-08-29 09:38:21,487 - INFO -   SHIBOR权重: 0.5\n", "2025-08-29 09:38:21,488 - INFO -   社融权重: 0.5\n", "2025-08-29 09:38:21,489 - INFO -   信贷冲量权重: 0.5\n", "2025-08-29 09:38:21,489 - INFO - ================================================================================\n", "2025-08-29 09:38:21,490 - INFO - ================================================================================\n", "2025-08-29 09:38:21,492 - INFO - 股债轮动回测引擎初始化\n", "2025-08-29 09:38:21,494 - INFO - ================================================================================\n", "2025-08-29 09:38:21,495 - INFO - 回测基础配置:\n", "2025-08-29 09:38:21,496 - INFO -   股票指数代码: H00906.CSI\n", "2025-08-29 09:38:21,496 - INFO -   债券指数代码: CBA00651.CS\n", "2025-08-29 09:38:21,497 - INFO -   交易成本: 0.30%\n", "2025-08-29 09:38:21,498 - INFO -   调仓频率: monthly\n", "2025-08-29 09:38:21,499 - INFO - 资产配置策略:\n", "2025-08-29 09:38:21,500 - INFO -   loose_monetary_loose_credit:\n", "2025-08-29 09:38:21,500 - INFO -     股票权重: 100%\n", "2025-08-29 09:38:21,501 - INFO -     债券权重: 0%\n", "2025-08-29 09:38:21,501 - INFO -   tight_monetary_loose_credit:\n", "2025-08-29 09:38:21,502 - INFO -     股票权重: 70%\n", "2025-08-29 09:38:21,502 - INFO -     债券权重: 30%\n", "2025-08-29 09:38:21,502 - INFO -   loose_monetary_tight_credit:\n", "2025-08-29 09:38:21,503 - INFO -     股票权重: 30%\n", "2025-08-29 09:38:21,503 - INFO -     债券权重: 70%\n", "2025-08-29 09:38:21,504 - INFO -   tight_monetary_tight_credit:\n", "2025-08-29 09:38:21,504 - INFO -     股票权重: 0%\n", "2025-08-29 09:38:21,504 - INFO -     债券权重: 100%\n", "2025-08-29 09:38:21,505 - INFO - ================================================================================\n", "2025-08-29 09:38:21,505 - INFO - 配置验证通过\n", "2025-08-29 09:38:21,506 - INFO - 股债轮动策略主程序初始化完成\n", "2025-08-29 09:38:21,507 - INFO - 开始运行股债轮动策略...\n", "2025-08-29 09:38:21,507 - INFO - 回测期间: 20170101 - 20250731\n", "2025-08-29 09:38:21,508 - INFO - 第一步：计算货币-信用周期因子\n", "2025-08-29 09:38:21,509 - INFO - ============================================================\n", "2025-08-29 09:38:21,510 - INFO - 开始计算综合因子（货币-信用周期）\n", "2025-08-29 09:38:21,510 - INFO - ============================================================\n", "2025-08-29 09:38:21,511 - INFO - 计算时间范围: 20170101 至 20250731\n", "2025-08-29 09:38:21,556 - INFO - 获取交易日历: 2327个交易日\n", "2025-08-29 09:38:21,557 - INFO - \n", "第一步：计算货币政策因子\n", "2025-08-29 09:38:21,558 - INFO - 开始计算货币因子...\n", "2025-08-29 09:38:21,559 - INFO -   开始计算DR007子因子...\n", "2025-08-29 09:38:21,560 - INFO -     DR007参数: 平滑期=20天, 比较期=60天\n", "2025-08-29 09:38:21,560 - INFO -     DR007逻辑: 平滑值环比下降为宽松(1)，上升为紧缩(0)\n", "2025-08-29 09:38:21,561 - INFO -     扩展开始日期: 20160814 (原始: 20161202)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["数据库连接成功\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-29 09:38:26,694 - INFO -     获取DR007原始数据: 2176条\n", "2025-08-29 09:38:26,696 - INFO -     DR007数据范围: 2016-08-15 00:00:00 至 2025-07-31 00:00:00\n", "2025-08-29 09:38:26,697 - INFO -     DR007统计: 均值=2.2028%, 标准差=0.4393%\n", "2025-08-29 09:38:26,702 - INFO -     DR007信号分布:\n", "2025-08-29 09:38:26,703 - INFO -       宽松(1): 1205次 (55.4%)\n", "2025-08-29 09:38:26,703 - INFO -       紧缩(0): 971次 (44.6%)\n", "2025-08-29 09:38:26,706 - INFO -     筛选后DR007数据: 2104条\n", "2025-08-29 09:38:26,707 - INFO -   DR007子因子计算完成\n", "2025-08-29 09:38:26,709 - INFO -   开始计算SHIBOR子因子...\n", "2025-08-29 09:38:26,710 - INFO -     SHIBOR参数: 平滑期=20天, 比较期=60天\n", "2025-08-29 09:38:26,711 - INFO -     SHIBOR逻辑: 平滑值环比下降为宽松(1)，上升为紧缩(0)\n", "2025-08-29 09:38:26,712 - INFO -     扩展开始日期: 20160814 (原始: 20161202)\n", "2025-08-29 09:38:31,176 - INFO -     获取SHIBOR原始数据: 2176条\n", "2025-08-29 09:38:31,178 - INFO -     SHIBOR数据范围: 2016-08-15 00:00:00 至 2025-07-31 00:00:00\n", "2025-08-29 09:38:31,179 - INFO -     SHIBOR统计: 均值=1.9376%, 标准差=0.5218%\n", "2025-08-29 09:38:31,182 - INFO -     SHIBOR信号分布:\n", "2025-08-29 09:38:31,182 - INFO -       宽松(1): 1118次 (51.4%)\n", "2025-08-29 09:38:31,183 - INFO -       紧缩(0): 1058次 (48.6%)\n", "2025-08-29 09:38:31,186 - INFO -     筛选后SHIBOR数据: 2104条\n", "2025-08-29 09:38:31,187 - INFO -   SHIBOR子因子计算完成\n", "2025-08-29 09:38:31,192 - INFO - 合并后货币因子数据: 2104条记录\n", "2025-08-29 09:38:31,193 - INFO - 货币因子权重: DR007=0.5, SHIBOR=0.5\n", "2025-08-29 09:38:31,196 - INFO - 货币因子信号分布:\n", "2025-08-29 09:38:31,197 - INFO -   宽松(loose): 1334次 (63.4%)\n", "2025-08-29 09:38:31,198 - INFO -   紧缩(tight): 770次 (36.6%)\n", "2025-08-29 09:38:31,198 - INFO - 货币因子计算完成，共2104条记录\n", "2025-08-29 09:38:31,199 - INFO - \n", "第二步：计算信用周期因子\n", "2025-08-29 09:38:31,200 - INFO - 开始计算信用因子...\n", "2025-08-29 09:38:31,697 - INFO - 信用因子计算完成，共104条记录\n", "2025-08-29 09:38:31,779 - INFO - 综合因子计算完成，共2083条记录\n", "2025-08-29 09:38:31,780 - INFO - 因子计算完成，共2083条记录\n", "2025-08-29 09:38:31,781 - INFO - 第二步：运行股债轮动回测\n", "2025-08-29 09:38:31,782 - INFO - ================================================================================\n", "2025-08-29 09:38:31,783 - INFO - 开始运行股债轮动回测\n", "2025-08-29 09:38:31,784 - INFO - ================================================================================\n", "2025-08-29 09:38:31,785 - INFO - 回测时间范围: 20170101 至 20250731\n", "2025-08-29 09:38:31,786 - INFO - 因子数据量: 2083条记录\n", "2025-08-29 09:38:31,787 - INFO - \n", "第一步：获取收益率数据\n", "2025-08-29 09:38:31,993 - INFO - 从Excel文件获取收益率数据成功: 2083条记录\n", "2025-08-29 09:38:31,994 - INFO - 收益率数据获取成功: 2083条记录\n", "2025-08-29 09:38:31,996 - INFO - 数据时间范围: 2017-01-03 00:00:00 至 2025-07-31 00:00:00\n", "2025-08-29 09:38:31,997 - INFO - \n", "第二步：生成交易信号\n", "2025-08-29 09:38:31,999 - INFO -   开始生成交易信号...\n", "2025-08-29 09:38:32,002 - INFO - 生成调仓日期: 104个\n", "2025-08-29 09:38:32,003 - INFO -   生成调仓日期: 104个\n", "2025-08-29 09:38:32,004 - INFO -   调仓日期范围: 2017-01-03 至 2025-07-31\n", "2025-08-29 09:38:32,085 - INFO -   信号分布统计:\n", "2025-08-29 09:38:32,086 - INFO -     tight_monetary_loose_credit: 19次 (18.3%) - 股票70%/债券30%\n", "2025-08-29 09:38:32,087 - INFO -     loose_monetary_loose_credit: 34次 (32.7%) - 股票100%/债券0%\n", "2025-08-29 09:38:32,088 - INFO -     loose_monetary_tight_credit: 33次 (31.7%) - 股票30%/债券70%\n", "2025-08-29 09:38:32,089 - INFO -     tight_monetary_tight_credit: 18次 (17.3%) - 股票0%/债券100%\n", "2025-08-29 09:38:32,089 - INFO -   交易信号生成完成，共104个调仓日\n", "2025-08-29 09:38:32,090 - INFO - 交易信号生成成功: 104个调仓日\n", "2025-08-29 09:38:32,091 - INFO - \n", "第三步：执行回测计算\n", "2025-08-29 09:38:33,984 - INFO - 回测执行完成，共2083个交易日\n", "2025-08-29 09:38:33,986 - INFO - 回测执行成功: 2083个交易日\n", "2025-08-29 09:38:33,986 - INFO - \n", "第四步：计算业绩指标\n", "2025-08-29 09:38:34,018 - INFO - \n", "关键业绩指标:\n", "2025-08-29 09:38:34,018 - INFO -   年化收益率: 6.45%\n", "2025-08-29 09:38:34,019 - INFO -   年化波动率: 0.00%\n", "2025-08-29 09:38:34,019 - INFO -   最大回撤: -18.99%\n", "2025-08-29 09:38:34,020 - INFO -   夏普比率: 0.353\n", "2025-08-29 09:38:34,021 - INFO -   换手率: 322.72%\n", "2025-08-29 09:38:34,021 - INFO - ================================================================================\n", "2025-08-29 09:38:34,022 - INFO - 股债轮动回测完成\n", "2025-08-29 09:38:34,024 - INFO - ================================================================================\n", "2025-08-29 09:38:34,025 - INFO - 回测完成\n", "2025-08-29 09:38:34,026 - INFO - 第三步：生成年度业绩统计\n", "2025-08-29 09:38:34,148 - INFO - \n", "================================================================================\n", "2025-08-29 09:38:34,149 - INFO - 年度业绩指标统计\n", "2025-08-29 09:38:34,149 - INFO - ================================================================================\n", "2025-08-29 09:38:34,156 - INFO -      年份  组合收益(%)  基准收益(%)  超额收益(%)  年化波动率(%)  最大回撤(%)  夏普比率  胜率(%)   赔率  换手率(%)\n", "   2017     9.35     4.51     4.83      9.25    -5.72  0.79  75.00 2.15   41.67\n", "   2018   -10.76   -11.88     1.13     12.17   -18.47 -1.05  58.33 1.25   54.55\n", "   2019    28.47    18.51     9.96     10.39    -3.94  2.55  33.33 5.31   63.64\n", "   2020    16.55    12.89     3.66     20.92   -15.52  0.70  66.67 0.83   36.36\n", "   2021     5.01     1.63     3.38     12.09   -10.18  0.25  58.33 1.69   54.55\n", "   2022    -8.99    -9.80     0.81     13.17   -16.73 -0.83  58.33 0.79   36.36\n", "   2023     0.82    -3.61     4.43      5.92    -8.65 -0.20  66.67 1.05   63.64\n", "   2024     8.65    12.64    -3.99     14.30   -11.49  0.46  50.00 0.71   81.82\n", "   2025     5.69     4.60     1.09      6.43    -3.94  0.57  42.86 1.54   33.33\n", "全样本(年化)     6.45     3.03     3.42     12.60   -18.99  0.35  58.25 1.13   49.51\n", "2025-08-29 09:38:34,156 - INFO - ================================================================================\n", "2025-08-29 09:38:34,157 - INFO - 年度业绩统计已保存到: ./results/stock_bond_rotation_annual_performance.csv\n", "2025-08-29 09:38:34,169 - INFO - 因子数据已保存到: ./results/stock_bond_rotation_factor_data.csv\n", "2025-08-29 09:38:34,172 - INFO - 信号数据已保存到: ./results/stock_bond_rotation_signals.csv\n", "2025-08-29 09:38:34,201 - INFO - 回测结果已保存到: ./results/stock_bond_rotation_backtest_results.csv\n", "2025-08-29 09:38:34,203 - INFO - 业绩报告已保存到: ./results/stock_bond_rotation_performance_report.txt\n", "2025-08-29 09:38:34,204 - INFO - 股债轮动策略业绩报告\n", "2025-08-29 09:38:34,204 - INFO - ==================================================\n", "2025-08-29 09:38:34,204 - INFO - 年化收益率: 6.45%\n", "2025-08-29 09:38:34,205 - INFO - 年化超额收益率: 3.39%\n", "2025-08-29 09:38:34,205 - INFO - 夏普比率: 0.353\n", "2025-08-29 09:38:34,206 - INFO - 最大回撤: -18.99%\n", "2025-08-29 09:38:34,206 - INFO - 胜率: 57.28%\n", "2025-08-29 09:38:34,207 - INFO - IC: 0.000\n", "2025-08-29 09:38:34,208 - INFO - 换手率: 322.72%\n", "2025-08-29 09:38:35,251 - INFO - 净值曲线图已保存\n", "2025-08-29 09:38:36,194 - INFO - 权重变化图已保存\n", "2025-08-29 09:38:37,459 - INFO - 回撤分析图已保存\n", "2025-08-29 09:38:38,679 - INFO - 因子分析图已保存\n", "2025-08-29 09:38:38,680 - INFO - 结果已保存到 ./results 和 ./charts 目录\n", "2025-08-29 09:38:38,681 - INFO - 股债轮动策略运行完成！\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "股债轮动策略关键业绩指标\n", "============================================================\n", "年化收益率: 6.45%\n", "基准年化收益率: 3.06%\n", "年化超额收益率: 3.39%\n", "年化波动率: 12.60%\n", "最大回撤: -18.99%\n", "夏普比率: 0.353\n", "胜率: 57.28%\n", "赔率: 1.179\n", "换手率: 322.72%\n", "交易天数: 2083\n", "============================================================\n"]}], "source": ["from main import *\n", "strategy = StockBondRotationMain('config.json')\n", "backtest_results = strategy.run_comprehensive_strategy()"]}], "metadata": {"kernelspec": {"display_name": "common", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}