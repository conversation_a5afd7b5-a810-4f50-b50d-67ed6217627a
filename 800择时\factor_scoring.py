"""
因子综合评分系统
实现各个因子的标准化处理和综合评分计算逻辑
基于新的三层架构：宏观（经济）、中观（市场）、微观（标的）
"""

import pandas as pd
import numpy as np
from macro_factors import MacroEconomicFactors
from meso_factors import MesoMarketFactors
from micro_factors import MicroTargetFactors
import warnings
warnings.filterwarnings('ignore')


def mad_clip(series, multiplier=1.5):
    """
    使用MAD（中位数绝对偏差）进行截尾处理

    Args:
        series: 待处理的数据序列
        multiplier: MAD倍数，默认1.5倍

    Returns:
        截尾后的数据序列
    """
    median = series.median()
    mad = np.median(np.abs(series - median))

    # 避免MAD为0的情况
    if mad == 0:
        return series

    # 计算截尾边界
    lower_bound = median - multiplier * mad
    upper_bound = median + multiplier * mad

    return np.clip(series, lower_bound, upper_bound)


class FactorScoring:
    """因子综合评分系统"""

    def __init__(self, database_manager, config=None):
        """
        初始化因子评分系统

        Args:
            database_manager: 数据库管理器实例
            config: 配置对象，包含因子参数
        """
        self.db = database_manager
        self.config = config
        self.macro_factors = MacroEconomicFactors(database_manager, config)
        self.meso_factors = MesoMarketFactors(database_manager, config)
        self.micro_factors = MicroTargetFactors(database_manager, config)
    
    def calculate_all_factors(self, index_code='000906.SH', start_date=None, end_date=None):
        """
        计算所有因子得分
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 包含所有因子得分的数据
        """
        print("正在计算宏观因子...")
        macro_scores = self.macro_factors.calculate_all_macro_factors(
            index_code, start_date, end_date
        )
        macro_scores['code'] = index_code
        
        print("正在计算中观因子...")
        meso_scores = self.meso_factors.calculate_all_meso_factors(
            index_code, start_date, end_date
        )
        
        print("正在计算微观因子...")
        micro_scores = self.micro_factors.calculate_all_micro_factors(
            index_code, start_date, end_date
        )

        # 获取交易日历
        trading_dates = self.db.get_trading_date_range(start_date, end_date)
        if trading_dates.empty:
            print("错误：无法获取交易日历")
            return pd.DataFrame()

        result = pd.DataFrame({'date': trading_dates})
        # 添加指数代码
        result['code'] = index_code

        # 合并宏观得分
        if not macro_scores.empty:
            result = result.merge(
                macro_scores[['date', 'macro_score']], 
                on='date', how='left'
            )
        else:
            result['macro_score'] = 0
        
        # 合并中观得分
        if not meso_scores.empty:
            result = result.merge(
                meso_scores[['date', 'meso_score']], 
                on='date', how='left'
            )
        else:
            result['meso_score'] = 0
        
        # 合并微观得分
        if not micro_scores.empty:
            result = result.merge(
                micro_scores[['date', 'micro_score']], 
                on='date', how='left'
            )
        else:
            result['micro_score'] = 0
        
        # 填充缺失值
        score_cols = ['macro_score', 'meso_score', 'micro_score']
        for col in score_cols:
            if col in result.columns:
                result[col] = result[col].ffill().fillna(0)
        
        return result
    
    def calculate_comprehensive_score(self, factor_scores, weights=None):
        """
        计算综合得分
        
        Args:
            factor_scores: 包含各因子得分的DataFrame
            weights: 权重字典，默认等权
            
        Returns:
            pandas.DataFrame: 包含综合得分的数据
        """
        if factor_scores.empty:
            return pd.DataFrame()
        
        # 默认等权重
        if weights is None:
            weights = {
                'macro_score': 1/3,
                'meso_score': 1/3,
                'micro_score': 1/3
            }
        
        result = factor_scores.copy()
        
        # 计算综合得分
        comprehensive_score = 0
        total_weight = 0
        
        for factor, weight in weights.items():
            if factor in result.columns:
                comprehensive_score += result[factor] * weight
                total_weight += weight
        
        if total_weight > 0:
            result['comprehensive_score'] = comprehensive_score / total_weight
        else:
            result['comprehensive_score'] = 0
        
        return result
    
    def generate_trading_signals(self, comprehensive_scores, signal_threshold=0):
        """
        生成交易信号
        
        根据综合得分生成交易信号，用于周度调仓
        
        Args:
            comprehensive_scores: 包含综合得分的DataFrame
            signal_threshold: 信号阈值
            
        Returns:
            pandas.DataFrame: 包含交易信号的数据
        """
        if comprehensive_scores.empty:
            return pd.DataFrame()
        
        result = comprehensive_scores.copy()
        
        # 生成交易信号
        result['signal'] = np.where(
            result['comprehensive_score'] > signal_threshold, 1,  # 看多信号
            np.where(result['comprehensive_score'] < -signal_threshold, -1, 0)  # 看空信号
        )
        
        # 计算持仓权重
        # 持有权益的仓位 = 0.5 + 指标分数 × 0.5
        result['equity_weight'] = 0.5 + result['comprehensive_score'] * 0.5
        
        # 确保权重在0-1之间
        result['equity_weight'] = np.clip(result['equity_weight'], 0, 1)
        
        # 货币基金权重
        result['money_fund_weight'] = 1 - result['equity_weight']
        
        return result

    def generate_single_factor_signals(self, factor_scores, factor_name, signal_threshold=0,
                                     position_base=0.5, position_multiplier=0.5):
        """
        生成单因子交易信号

        Args:
            factor_scores: 包含因子得分的DataFrame
            factor_name: 因子名称（如'production_score', 'macro_score'等）
            signal_threshold: 信号阈值
            position_base: 基础仓位
            position_multiplier: 仓位调整倍数

        Returns:
            pandas.DataFrame: 包含单因子交易信号的数据
        """
        if factor_scores.empty or factor_name not in factor_scores.columns:
            return pd.DataFrame()

        result = factor_scores[['date', 'code', factor_name]].copy()

        # 生成交易信号
        result['signal'] = np.where(
            result[factor_name] > signal_threshold, 1,
            np.where(result[factor_name] < -signal_threshold, -1, 0)
        )

        # 根据信号调整仓位
        result['equity_weight'] = position_base + result[factor_name] * position_multiplier
        result['equity_weight'] = np.clip(result['equity_weight'], 0, 1)

        # 货币基金权重
        result['money_fund_weight'] = 1 - result['equity_weight']

        # 重命名因子得分列为统一名称
        result['factor_score'] = result[factor_name]

        return result[['date', 'code', 'factor_score', 'signal', 'equity_weight', 'money_fund_weight']]

    def generate_monthly_signals(self, factor_scores, signal_threshold=0):
        """
        生成月度交易信号

        Args:
            factor_scores: 包含因子得分的DataFrame
            signal_threshold: 信号阈值

        Returns:
            pandas.DataFrame: 包含月度交易信号的数据
        """
        if factor_scores.empty:
            return pd.DataFrame()

        # 获取交易日历
        start_date = factor_scores['date'].min().strftime('%Y%m%d')
        end_date = factor_scores['date'].max().strftime('%Y%m%d')
        trading_dates = self.db.get_trading_date_range(start_date, end_date)

        if trading_dates.empty:
            return pd.DataFrame()

        # 获取每月最后一个交易日
        monthly_dates = [pd.to_datetime(start_date)]
        for date in trading_dates:
            month_end = date + pd.offsets.MonthEnd(0)
            month_trading_dates = trading_dates[
                (trading_dates >= date.replace(day=1)) &
                (trading_dates <= month_end)
            ]
            if len(month_trading_dates) > 0 and date == month_trading_dates.max():
                monthly_dates.append(date)

        # 筛选月末的因子数据
        factor_scores['date'] = pd.to_datetime(factor_scores['date'])
        monthly_factor_data = factor_scores[factor_scores['date'].isin(monthly_dates)].copy()

        if monthly_factor_data.empty:
            return pd.DataFrame()

        # 生成信号（基于综合得分）
        if 'comprehensive_score' in monthly_factor_data.columns:
            score_col = 'comprehensive_score'
        elif 'total_score' in monthly_factor_data.columns:
            score_col = 'total_score'
        else:
            return pd.DataFrame()

        # 生成权重信号
        monthly_factor_data['equity_weight'] = np.where(
            monthly_factor_data[score_col] >= signal_threshold, 0.8, 0.2
        )
        monthly_factor_data['money_fund_weight'] = 1 - monthly_factor_data['equity_weight']

        return monthly_factor_data[['date', 'code', score_col, 'equity_weight', 'money_fund_weight']].reset_index(drop=True)

    def calculate_factor_category_score(self, factor_scores, category='macro'):
        """
        计算因子大类得分

        Args:
            factor_scores: 包含各因子得分的DataFrame
            category: 因子大类（'macro', 'meso', 'micro', 'technical'）

        Returns:
            pandas.DataFrame: 包含大类因子得分的数据
        """
        if factor_scores.empty:
            return pd.DataFrame()

        result = factor_scores[['date', 'code']].copy()

        if category == 'macro':
            # 宏观因子大类
            macro_cols = [col for col in factor_scores.columns if 'macro' in col.lower() or
                         col in ['production_score', 'consumption_score', 'investment_score',
                                'monetary_score', 'exchange_rate_score', 'bond_score', 'credit_score']]
            if macro_cols:
                result['macro_category_score'] = factor_scores[macro_cols].mean(axis=1, skipna=True)
            else:
                result['macro_category_score'] = 0

        elif category == 'meso':
            # 中观因子大类
            meso_cols = [col for col in factor_scores.columns if 'meso' in col.lower() or
                        col in ['margin_trading_score', 'turnover_trend_score', 'market_money_flow_score',
                               'main_money_flow_score', 'vix_score', 'capital_flow_score', 'market_sentiment_score']]
            if meso_cols:
                result['meso_category_score'] = factor_scores[meso_cols].mean(axis=1, skipna=True)
            else:
                result['meso_category_score'] = 0

        elif category == 'micro':
            # 微观因子大类
            micro_cols = [col for col in factor_scores.columns if 'micro' in col.lower() or
                         col in ['value_score', 'momentum_score', 'roe_score', 'liquidity_score']]
            if micro_cols:
                result['micro_category_score'] = factor_scores[micro_cols].mean(axis=1, skipna=True)
            else:
                result['micro_category_score'] = 0

        elif category == 'technical':
            # 技术因子大类
            technical_cols = [col for col in factor_scores.columns if
                             col in ['bias_score', 'aroon_score', 'adx_score', 'uo_score',
                                    'atr_score', 'udvd_score', 'new_high_low_score']]
            if technical_cols:
                result['technical_category_score'] = factor_scores[technical_cols].mean(axis=1, skipna=True)
            else:
                result['technical_category_score'] = 0

        return result

    def get_weekly_signals(self, signals_df):
        """
        获取周度信号
        
        
        使用每周最后一个交易日信号，用其后一个交易日的收盘价进行买卖
        
        Args:
            signals_df: 包含交易信号的DataFrame
            
        Returns:
            pandas.DataFrame: 周度交易信号
        """
        if signals_df.empty:
            return pd.DataFrame()
        
        result = signals_df.copy()
        result['date'] = pd.to_datetime(result['date'])
        
        # 添加周信息
        result['year_week'] = result['date'].dt.strftime('%Y-%U')
        # 添加执行日期
        result['execution_date'] = result['date'].shift(-1)
        
        # 获取每周最后一个交易日的信号
        weekly_signals = result.groupby('year_week').last().reset_index()
        
        return weekly_signals
    
    def standardize_scores(self, scores_df, method='zscore', window=252):
        """
        标准化因子得分
        
        Args:
            scores_df: 包含因子得分的DataFrame
            method: 标准化方法，'zscore'或'minmax'
            window: 滚动窗口大小
            
        Returns:
            pandas.DataFrame: 标准化后的得分
        """
        if scores_df.empty:
            return pd.DataFrame()
        
        result = scores_df.copy()
        score_cols = [col for col in result.columns if col.endswith('_score')]
        
        for col in score_cols:
            if method == 'zscore':
                # 滚动Z-score标准化
                rolling_mean = result[col].rolling(window, min_periods=30).mean()
                rolling_std = result[col].rolling(window, min_periods=30).std()
                result[f'{col}_standardized'] = (result[col] - rolling_mean) / rolling_std
                
                # 处理无穷大和NaN值
                result[f'{col}_standardized'] = result[f'{col}_standardized'].replace(
                    [np.inf, -np.inf], np.nan
                ).fillna(0)
                
                # MAD截尾处理（3倍MAD）
                result[f'{col}_standardized'] = mad_clip(
                    result[f'{col}_standardized'], multiplier=3
                )
                
            elif method == 'minmax':
                # 滚动Min-Max标准化
                rolling_min = result[col].rolling(window, min_periods=30).min()
                rolling_max = result[col].rolling(window, min_periods=30).max()
                result[f'{col}_standardized'] = (
                    (result[col] - rolling_min) / (rolling_max - rolling_min) * 2 - 1
                )
                
                
                # 处理除零错误
                result[f'{col}_standardized'] = result[f'{col}_standardized'].fillna(0)
        
        return result
    
    def get_factor_summary(self, factor_scores):
        """
        获取因子得分汇总统计
        
        Args:
            factor_scores: 包含因子得分的DataFrame
            
        Returns:
            pandas.DataFrame: 因子统计汇总
        """
        if factor_scores.empty:
            return pd.DataFrame()
        
        score_cols = [col for col in factor_scores.columns if col.endswith('_score')]
        
        summary_stats = []
        for col in score_cols:
            stats = {
                'factor': col,
                'mean': factor_scores[col].mean(),
                'std': factor_scores[col].std(),
                'min': factor_scores[col].min(),
                'max': factor_scores[col].max(),
                'positive_ratio': (factor_scores[col] > 0).mean(),
                'negative_ratio': (factor_scores[col] < 0).mean(),
                'zero_ratio': (factor_scores[col] == 0).mean()
            }
            summary_stats.append(stats)
        
        return pd.DataFrame(summary_stats)
