"""
股债轮动策略因子计算模块
基于货币-信用周期模型构造因子
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class StockBondRotationFactors:
    """股债轮动策略因子计算类"""

    def __init__(self, database_manager, config=None):
        """
        初始化因子计算器

        Args:
            database_manager: 数据库管理器实例
            config: 配置管理器实例
        """
        self.db = database_manager
        self.config = config
        if config:
            self.factors_config = config.get_factors_config()
        else:
            # 默认配置
            self.factors_config = {
                'monetary': {
                    'dr007_smooth_period': 240,
                    'dr007_compare_period': 60,
                    'shibor_smooth_period': 240,
                    'shibor_compare_period': 60
                },
                'credit': {
                    'social_financing_smooth_period': 12,
                    'social_financing_compare_period': 3,
                    'credit_impulse_smooth_period': 12,
                    'credit_impulse_compare_period': 3
                },
                'weights': {
                    'dr007_weight': 0.5,
                    'shibor_weight': 0.5,
                    'social_financing_weight': 0.5,
                    'credit_impulse_weight': 0.5
                }
            }

        # 记录因子配置参数
        logging.info("="*80)
        logging.info("股债轮动因子计算器初始化")
        logging.info("="*80)
        logging.info("货币政策因子参数:")
        logging.info(f"  DR007平滑期: {self.factors_config['monetary']['dr007_smooth_period']}天")
        logging.info(f"  DR007比较期: {self.factors_config['monetary']['dr007_compare_period']}天")
        logging.info(f"  SHIBOR平滑期: {self.factors_config['monetary']['shibor_smooth_period']}天")
        logging.info(f"  SHIBOR比较期: {self.factors_config['monetary']['shibor_compare_period']}天")

        logging.info("信用周期因子参数:")
        logging.info(f"  社融平滑期: {self.factors_config['credit']['social_financing_smooth_period']}月")
        logging.info(f"  社融比较期: {self.factors_config['credit']['social_financing_compare_period']}月")
        logging.info(f"  信贷冲量平滑期: {self.factors_config['credit']['credit_impulse_smooth_period']}月")
        logging.info(f"  信贷冲量比较期: {self.factors_config['credit']['credit_impulse_compare_period']}月")

        logging.info("因子权重参数:")
        logging.info(f"  DR007权重: {self.factors_config['weights']['dr007_weight']}")
        logging.info(f"  SHIBOR权重: {self.factors_config['weights']['shibor_weight']}")
        logging.info(f"  社融权重: {self.factors_config['weights']['social_financing_weight']}")
        logging.info(f"  信贷冲量权重: {self.factors_config['weights']['credit_impulse_weight']}")
        logging.info("="*80)
        
    def calculate_monetary_factor(self, start_date, end_date):
        """
        计算货币因子

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 货币因子数据
        """
        try:
            logging.info("开始计算货币因子...")

            # 获取DR007数据
            dr007_data = self._get_dr007_factor(start_date, end_date)

            # 获取SHIBOR数据
            shibor_data = self._get_shibor_factor(start_date, end_date)

            if dr007_data.empty and shibor_data.empty:
                logging.warning("DR007和SHIBOR数据都为空，无法计算货币因子")
                return pd.DataFrame()

            # 合并货币因子
            monetary_factor = pd.merge(dr007_data, shibor_data, on='date', how='outer')
            monetary_factor = monetary_factor.sort_values('date').reset_index(drop=True)

            logging.info(f"合并后货币因子数据: {len(monetary_factor)}条记录")

            # 计算综合货币因子得分
            dr007_weight = self.factors_config['weights']['dr007_weight']
            shibor_weight = self.factors_config['weights']['shibor_weight']

            logging.info(f"货币因子权重: DR007={dr007_weight}, SHIBOR={shibor_weight}")

            # 填充缺失信号为0
            monetary_factor['dr007_signal'] = monetary_factor['dr007_signal'].fillna(0)
            monetary_factor['shibor_signal'] = monetary_factor['shibor_signal'].fillna(0)

            monetary_factor['monetary_score'] = (
                monetary_factor['dr007_signal'] * dr007_weight +
                monetary_factor['shibor_signal'] * shibor_weight
            )

            # 货币因子信号：得分>0.5为宽松，否则为紧缩
            monetary_factor['monetary_signal'] = np.where(
                monetary_factor['monetary_score'] > 0, 'loose', 'tight'
            )

            # 统计货币因子信号分布
            signal_stats = monetary_factor['monetary_signal'].value_counts()
            logging.info(f"货币因子信号分布:")
            logging.info(f"  宽松(loose): {signal_stats.get('loose', 0)}次 ({signal_stats.get('loose', 0)/len(monetary_factor)*100:.1f}%)")
            logging.info(f"  紧缩(tight): {signal_stats.get('tight', 0)}次 ({signal_stats.get('tight', 0)/len(monetary_factor)*100:.1f}%)")

            logging.info(f"货币因子计算完成，共{len(monetary_factor)}条记录")
            return monetary_factor

        except Exception as e:
            logging.error(f"货币因子计算失败: {e}")
            return pd.DataFrame()
    
    def _get_dr007_factor(self, start_date, end_date):
        """计算DR007因子"""
        try:
            logging.info("  开始计算DR007子因子...")

            # 扩展开始日期以获取足够的历史数据
            smooth_period = self.factors_config['monetary']['dr007_smooth_period']
            compare_period = self.factors_config['monetary']['dr007_compare_period']

            logging.info(f"    DR007参数: 平滑期={smooth_period}天, 比较期={compare_period}天")
            logging.info(f"    DR007逻辑: 平滑值环比下降为宽松(1)，上升为紧缩(0)")

            if start_date:
                extended_start = pd.to_datetime(start_date) - pd.DateOffset(days=smooth_period + compare_period + 30)
                extended_start = extended_start.strftime('%Y%m%d')
                logging.info(f"    扩展开始日期: {extended_start} (原始: {start_date})")
            else:
                extended_start = start_date

            # 获取DR007数据
            dr007_data = self.db.get_daily_macro_data(extended_start, end_date)

            if dr007_data.empty or 'dr007' not in dr007_data.columns:
                logging.warning("    DR007数据为空或不存在")
                return pd.DataFrame()

            logging.info(f"    获取DR007原始数据: {len(dr007_data)}条")
            logging.info(f"    DR007数据范围: {dr007_data['date'].min()} 至 {dr007_data['date'].max()}")
            logging.info(f"    DR007统计: 均值={dr007_data['dr007'].mean():.4f}%, 标准差={dr007_data['dr007'].std():.4f}%")

            # 计算平滑值
            dr007_data['dr007_smooth'] = dr007_data['dr007'].rolling(
                window=smooth_period, min_periods=1
            ).mean()

            # 计算环比变化
            dr007_data['dr007_change'] = dr007_data['dr007_smooth'].pct_change(compare_period)

            # 生成信号：下降为宽松(1)，上升为紧缩(0)
            dr007_data['dr007_signal'] = np.where(
                dr007_data['dr007_change'] < 0, 1, 0
            )

            # 统计信号分布
            valid_signals = dr007_data['dr007_signal'].dropna()
            if len(valid_signals) > 0:
                signal_stats = valid_signals.value_counts()
                logging.info(f"    DR007信号分布:")
                logging.info(f"      宽松(1): {signal_stats.get(1, 0)}次 ({signal_stats.get(1, 0)/len(valid_signals)*100:.1f}%)")
                logging.info(f"      紧缩(0): {signal_stats.get(0, 0)}次 ({signal_stats.get(0, 0)/len(valid_signals)*100:.1f}%)")

            # 筛选日期范围
            if start_date:
                dr007_data = dr007_data[dr007_data['date'] >= start_date]
                logging.info(f"    筛选后DR007数据: {len(dr007_data)}条")

            logging.info("  DR007子因子计算完成")
            return dr007_data[['date', 'dr007', 'dr007_smooth', 'dr007_change', 'dr007_signal']].reset_index(drop=True)

        except Exception as e:
            logging.error(f"DR007因子计算失败: {e}")
            return pd.DataFrame()
    
    def _get_shibor_factor(self, start_date, end_date):
        """计算SHIBOR因子"""
        try:
            logging.info("  开始计算SHIBOR子因子...")

            # 扩展开始日期以获取足够的历史数据
            smooth_period = self.factors_config['monetary']['shibor_smooth_period']
            compare_period = self.factors_config['monetary']['shibor_compare_period']

            logging.info(f"    SHIBOR参数: 平滑期={smooth_period}天, 比较期={compare_period}天")
            logging.info(f"    SHIBOR逻辑: 平滑值环比下降为宽松(1)，上升为紧缩(0)")

            if start_date:
                extended_start = pd.to_datetime(start_date) - pd.DateOffset(days=smooth_period + compare_period + 30)
                extended_start = extended_start.strftime('%Y%m%d')
                logging.info(f"    扩展开始日期: {extended_start} (原始: {start_date})")
            else:
                extended_start = start_date

            # 获取SHIBOR数据
            shibor_data = self.db.get_daily_macro_data(extended_start, end_date)

            if shibor_data.empty or 'shibor' not in shibor_data.columns:
                logging.warning("    SHIBOR数据为空或不存在")
                return pd.DataFrame()

            logging.info(f"    获取SHIBOR原始数据: {len(shibor_data)}条")
            logging.info(f"    SHIBOR数据范围: {shibor_data['date'].min()} 至 {shibor_data['date'].max()}")
            logging.info(f"    SHIBOR统计: 均值={shibor_data['shibor'].mean():.4f}%, 标准差={shibor_data['shibor'].std():.4f}%")

            # 计算平滑值
            shibor_data['shibor_smooth'] = shibor_data['shibor'].rolling(
                window=smooth_period, min_periods=1
            ).mean()

            # 计算环比变化
            shibor_data['shibor_change'] = shibor_data['shibor_smooth'].pct_change(compare_period)

            # 生成信号：下降为宽松(1)，上升为紧缩(0)
            shibor_data['shibor_signal'] = np.where(
                shibor_data['shibor_change'] < 0, 1, 0
            )

            # 统计信号分布
            valid_signals = shibor_data['shibor_signal'].dropna()
            if len(valid_signals) > 0:
                signal_stats = valid_signals.value_counts()
                logging.info(f"    SHIBOR信号分布:")
                logging.info(f"      宽松(1): {signal_stats.get(1, 0)}次 ({signal_stats.get(1, 0)/len(valid_signals)*100:.1f}%)")
                logging.info(f"      紧缩(0): {signal_stats.get(0, 0)}次 ({signal_stats.get(0, 0)/len(valid_signals)*100:.1f}%)")

            # 筛选日期范围
            if start_date:
                shibor_data = shibor_data[shibor_data['date'] >= start_date]
                logging.info(f"    筛选后SHIBOR数据: {len(shibor_data)}条")

            logging.info("  SHIBOR子因子计算完成")
            return shibor_data[['date', 'shibor', 'shibor_smooth', 'shibor_change', 'shibor_signal']].reset_index(drop=True)

        except Exception as e:
            logging.error(f"SHIBOR因子计算失败: {e}")
            return pd.DataFrame()
    
    def calculate_credit_factor(self, start_date, end_date):
        """
        计算信用因子
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pandas.DataFrame: 信用因子数据
        """
        try:
            logging.info("开始计算信用因子...")
            
            # 获取社融因子
            social_financing_data = self._get_social_financing_factor(start_date, end_date)
            
            # 获取信贷脉冲因子
            loan_yoy_data = self._get_loan_yoy_factor(start_date, end_date)
            
            # 合并信用因子
            credit_factor = pd.merge(social_financing_data, loan_yoy_data, on='date', how='outer')
            credit_factor = credit_factor.sort_values('date').reset_index(drop=True)
            
            # 计算综合信用因子得分
            social_financing_weight = self.factors_config['weights']['social_financing_weight']
            credit_impulse_weight = self.factors_config['weights']['credit_impulse_weight']
            
            credit_factor['credit_score'] = (
                credit_factor['social_financing_signal'] * social_financing_weight +
                credit_factor['loan_yoy_signal'] * credit_impulse_weight
            )
            
            # 信用因子信号：1为扩张，0为紧缩
            credit_factor['credit_signal'] = np.where(
                credit_factor['credit_score'] > 0, 'expansion', 'contraction'
            )
            
            logging.info(f"信用因子计算完成，共{len(credit_factor)}条记录")
            return credit_factor
            
        except Exception as e:
            logging.error(f"信用因子计算失败: {e}")
            return pd.DataFrame()
    
    def _get_social_financing_factor(self, start_date, end_date):
        """计算社融因子"""
        try:
            # 扩展开始日期以获取足够的历史数据
            smooth_period = self.factors_config['credit']['social_financing_smooth_period']
            compare_period = self.factors_config['credit']['social_financing_compare_period']

            if start_date:
                extended_start = pd.to_datetime(start_date) - pd.DateOffset(months=smooth_period + compare_period + 6)
                extended_start = extended_start.strftime('%Y%m%d')
            else:
                extended_start = start_date

            # 获取社融数据
            social_financing_data = self.db.get_monthly_macro_data(extended_start, end_date)

            if social_financing_data.empty or 'social_financing_yoy' not in social_financing_data.columns:
                logging.warning("社融数据为空或不存在")
                return pd.DataFrame()

            # 重命名列
            social_financing_data = social_financing_data.rename(columns={'social_financing_yoy': 'social_financing'})

            # 计算平滑值
            social_financing_data['social_financing_smooth'] = social_financing_data['social_financing'].rolling(
                window=smooth_period, min_periods=1
            ).mean()

            # 计算环比变化（相比3个月前）
            social_financing_data['social_financing_change'] = social_financing_data['social_financing_smooth'].pct_change(compare_period)

            # 生成信号：上升为扩张(1)，下降为紧缩(0)
            social_financing_data['social_financing_signal'] = np.where(
                social_financing_data['social_financing_change'] > 0, 1, 0
            )

            # 筛选日期范围
            if start_date:
                social_financing_data = social_financing_data[social_financing_data['date'] >= start_date]

            return social_financing_data[['date', 'social_financing', 'social_financing_smooth',
                                       'social_financing_change', 'social_financing_signal']].reset_index(drop=True)

        except Exception as e:
            logging.error(f"社融因子计算失败: {e}")
            return pd.DataFrame()
    
    def _get_loan_yoy_factor(self, start_date, end_date):
        """计算信贷脉冲因子"""
        try:
            # 扩展开始日期以获取足够的历史数据
            smooth_period = self.factors_config['credit']['credit_impulse_smooth_period']
            compare_period = self.factors_config['credit']['credit_impulse_compare_period']

            if start_date:
                extended_start = pd.to_datetime(start_date) - pd.DateOffset(months=smooth_period + compare_period + 6)
                extended_start = extended_start.strftime('%Y%m%d')
            else:
                extended_start = start_date

            # 获取中长期贷款数据
            credit_data = self.db.get_monthly_macro_data(extended_start, end_date)

            if credit_data.empty or 'loan_yoy' not in credit_data.columns:
                logging.warning("信贷数据为空或不存在")
                return pd.DataFrame()

            # 计算平滑值
            credit_data['loan_yoy_smooth'] = credit_data['loan_yoy'].rolling(
                window=smooth_period, min_periods=1
            ).mean()

            # 计算环比变化
            credit_data['loan_yoy_change'] = credit_data['loan_yoy'].pct_change(compare_period)

            # 生成信号：上升为扩张(1)，下降为紧缩(0)
            credit_data['loan_yoy_signal'] = np.where(
                credit_data['loan_yoy_change'] > 0, 1, 0
            )

            # 筛选日期范围
            if start_date:
                credit_data = credit_data[credit_data['date'] >= start_date]

            return credit_data[['date', 'loan_yoy', 'loan_yoy_smooth',
                              'loan_yoy_change', 'loan_yoy_signal']].reset_index(drop=True)

        except Exception as e:
            logging.error(f"信贷脉冲因子计算失败: {e}")
            return pd.DataFrame()
    
    def calculate_comprehensive_factor(self, start_date, end_date):
        """
        计算综合因子（货币-信用周期）

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 综合因子数据
        """
        try:
            logging.info("="*60)
            logging.info("开始计算综合因子（货币-信用周期）")
            logging.info("="*60)
            logging.info(f"计算时间范围: {start_date} 至 {end_date}")

            extended_start = pd.to_datetime(start_date) - pd.DateOffset(days=30)
            extended_start_str = extended_start.strftime('%Y%m%d')

            # 获取交易日历
            trading_dates = self.db.get_trading_date_range(extended_start, end_date)
            if trading_dates.empty:
                logging.error("错误：无法获取交易日历")
                return pd.DataFrame()

            logging.info(f"获取交易日历: {len(trading_dates)}个交易日")

            # 计算货币因子
            logging.info("\n第一步：计算货币政策因子")
            monetary_factor = self.calculate_monetary_factor(extended_start_str, end_date)

            # 计算信用因子
            logging.info("\n第二步：计算信用周期因子")
            credit_factor = self.calculate_credit_factor(extended_start_str, end_date)
            
            if monetary_factor.empty or credit_factor.empty:
                logging.error("货币因子或信用因子为空")
                return pd.DataFrame()
            
            # 合并因子
            comprehensive_factor = pd.merge(
                monetary_factor[['date', 'monetary_signal', 'monetary_score']], 
                credit_factor[['date', 'credit_signal', 'credit_score']], 
                on='date', how='outer'
            )
            
            comprehensive_factor = comprehensive_factor.sort_values('date').reset_index(drop=True)
            
            # 前向填充缺失值
            comprehensive_factor = comprehensive_factor.ffill()
            
            # 生成资产配置信号
            comprehensive_factor['allocation_signal'] = comprehensive_factor.apply(
                self._get_allocation_signal, axis=1
            )
            
            if start_date:
                comprehensive_factor = comprehensive_factor[comprehensive_factor['date'] >= start_date]
            logging.info(f"综合因子计算完成，共{len(comprehensive_factor)}条记录")
            return comprehensive_factor
            
        except Exception as e:
            logging.error(f"综合因子计算失败: {e}")
            return pd.DataFrame()
    
    def _get_allocation_signal(self, row):
        """
        根据货币-信用周期生成资产配置信号
        
        Args:
            row: 数据行
            
        Returns:
            str: 资产配置信号
        """
        monetary_signal = row['monetary_signal']
        credit_signal = row['credit_signal']
        
        if monetary_signal == 'loose' and credit_signal == 'expansion':
            return 'loose_monetary_loose_credit'  # 100%股票
        elif monetary_signal == 'tight' and credit_signal == 'expansion':
            return 'tight_monetary_loose_credit'  # 70%股票+30%债券
        elif monetary_signal == 'loose' and credit_signal == 'contraction':
            return 'loose_monetary_tight_credit'  # 30%股票+70%债券
        else:  # tight monetary + tight credit
            return 'tight_monetary_tight_credit'  # 100%债券

